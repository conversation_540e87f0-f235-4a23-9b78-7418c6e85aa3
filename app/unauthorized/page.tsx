'use client';

import { usePermissions } from '@/hooks/use-permissions';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertTriangle,
  ArrowLeft,
  Building2,
  Shield,
  Key,
  Users,
  Home,
  User,
  Sparkles,
  Lock,
  Eye,
  Settings,
} from 'lucide-react';
import Link from 'next/link';

export default function UnauthorizedPage() {
  const { roles, permissions, orgRoles, getCurrentOrgContext } = usePermissions();

  // Get current org context safely
  const currentOrgId = getCurrentOrgContext();
  const currentOrg =
    currentOrgId && orgRoles[currentOrgId]
      ? {
          orgId: currentOrgId,
          orgName: orgRoles[currentOrgId].orgName,
          role: orgRoles[currentOrgId].role,
        }
      : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#0f0f0f] to-[#1a1a2e] relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-violet-500/5 to-fuchsia-500/5 rounded-full blur-3xl animate-pulse delay-500" />
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="max-w-4xl w-full">
          {/* Main unauthorized card */}
          <Card className="bg-gradient-to-br from-[#1a1a2e]/80 via-[#262131]/80 to-[#1a1a2e]/80 border-[#3b3b3b]/50 backdrop-blur-xl shadow-2xl">
            <CardContent className="p-8 md:p-12">
              {/* Header section */}
              <div className="text-center mb-12">
                <div className="relative inline-flex items-center justify-center w-20 h-20 mb-6">
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 to-orange-500/20 rounded-full blur-xl animate-pulse" />
                  <div className="relative bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-full p-5 border border-red-500/20">
                    <Lock className="w-10 h-10 text-red-400" />
                  </div>
                </div>

                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent mb-4">
                  Access Restricted
                </h1>

                <p className="text-lg text-gray-400 max-w-2xl mx-auto leading-relaxed">
                  You don't have the required permissions to access this resource. Your current
                  access level is shown below.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                {/* Current Organization Context */}
                <Card className="bg-gradient-to-br from-blue-500/5 to-cyan-500/5 border-blue-500/20 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-blue-500/10 rounded-lg">
                        <Building2 className="w-5 h-5 text-blue-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Organization Context</h3>
                    </div>

                    {currentOrg ? (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Organization</span>
                          <span className="text-white font-medium">{currentOrg.orgName}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Your Role</span>
                          <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 capitalize">
                            {currentOrg.role}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Scope</span>
                          <span className="text-blue-300 text-sm">Organization-specific</span>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center py-8 text-gray-500">
                        <div className="text-center">
                          <Users className="w-8 h-8 mx-auto mb-2 opacity-50" />
                          <p>No organization selected</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Access Summary */}
                <Card className="bg-gradient-to-br from-purple-500/5 to-pink-500/5 border-purple-500/20 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-purple-500/10 rounded-lg">
                        <Shield className="w-5 h-5 text-purple-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Access Summary</h3>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-white/5 rounded-lg border border-white/10">
                        <div className="text-2xl font-bold text-purple-400 mb-1">
                          {roles.length}
                        </div>
                        <div className="text-sm text-gray-400">Active Roles</div>
                      </div>
                      <div className="text-center p-4 bg-white/5 rounded-lg border border-white/10">
                        <div className="text-2xl font-bold text-cyan-400 mb-1">
                          {permissions.length}
                        </div>
                        <div className="text-sm text-gray-400">Permissions</div>
                      </div>
                      <div className="text-center p-4 bg-white/5 rounded-lg border border-white/10">
                        <div className="text-2xl font-bold text-green-400 mb-1">
                          {Object.keys(orgRoles).length}
                        </div>
                        <div className="text-sm text-gray-400">Organizations</div>
                      </div>
                      <div className="text-center p-4 bg-white/5 rounded-lg border border-white/10">
                        <div className="text-2xl font-bold text-orange-400 mb-1">
                          {
                            Object.values(orgRoles).filter((org) =>
                              ['admin', 'owner'].includes(org.role),
                            ).length
                          }
                        </div>
                        <div className="text-sm text-gray-400">Admin Access</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Detailed Access Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                {/* Current Roles */}
                <Card className="bg-gradient-to-br from-green-500/5 to-emerald-500/5 border-green-500/20 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-green-500/10 rounded-lg">
                        <User className="w-5 h-5 text-green-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Current Roles</h3>
                      <Badge
                        variant="outline"
                        className="ml-auto border-green-500/30 text-green-300"
                      >
                        {roles.length}
                      </Badge>
                    </div>

                    <div className="space-y-2 max-h-32 overflow-y-auto custom-scrollbar">
                      {roles.length > 0 ? (
                        roles.map((role, index) => (
                          <div key={role} className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full" />
                            <Badge
                              variant="secondary"
                              className="bg-green-500/10 text-green-300 border-green-500/30 capitalize"
                            >
                              {role}
                            </Badge>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-4 text-gray-500">
                          <Eye className="w-6 h-6 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No roles assigned</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Current Permissions */}
                <Card className="bg-gradient-to-br from-orange-500/5 to-red-500/5 border-orange-500/20 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-orange-500/10 rounded-lg">
                        <Key className="w-5 h-5 text-orange-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Permissions</h3>
                      <Badge
                        variant="outline"
                        className="ml-auto border-orange-500/30 text-orange-300"
                      >
                        {permissions.length}
                      </Badge>
                    </div>

                    <div className="max-h-32 overflow-y-auto custom-scrollbar">
                      {permissions.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {permissions.slice(0, 8).map((permission) => (
                            <Badge
                              key={permission}
                              variant="outline"
                              className="text-xs border-orange-500/30 text-orange-300 bg-orange-500/5"
                            >
                              {permission.replace(/([A-Z])/g, ' $1').trim()}
                            </Badge>
                          ))}
                          {permissions.length > 8 && (
                            <Badge
                              variant="outline"
                              className="text-xs border-gray-500/30 text-gray-400"
                            >
                              +{permissions.length - 8} more
                            </Badge>
                          )}
                        </div>
                      ) : (
                        <div className="text-center py-4 text-gray-500">
                          <Lock className="w-6 h-6 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No permissions granted</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* All Organizations */}
              {Object.keys(orgRoles).length > 0 && (
                <Card className="bg-gradient-to-br from-violet-500/5 to-purple-500/5 border-violet-500/20 backdrop-blur-sm mb-12">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="p-2 bg-violet-500/10 rounded-lg">
                        <Building2 className="w-5 h-5 text-violet-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Organization Access</h3>
                      <Badge
                        variant="outline"
                        className="ml-auto border-violet-500/30 text-violet-300"
                      >
                        {Object.keys(orgRoles).length} organizations
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(orgRoles).map(([orgId, orgRole]) => (
                        <div
                          key={orgId}
                          className="group p-4 bg-gradient-to-r from-white/5 to-white/10 rounded-lg border border-white/10 hover:border-violet-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-violet-500/10"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium text-white group-hover:text-violet-300 transition-colors">
                                {orgRole.orgName}
                              </h4>
                              <p className="text-xs text-gray-500 mt-1 font-mono">
                                {orgId.substring(0, 12)}...
                              </p>
                            </div>
                            <Badge
                              className={`capitalize ml-3 ${
                                ['admin', 'owner'].includes(orgRole.role)
                                  ? 'bg-green-500/20 text-green-300 border-green-500/30'
                                  : orgRole.role === 'member'
                                    ? 'bg-blue-500/20 text-blue-300 border-blue-500/30'
                                    : 'bg-gray-500/20 text-gray-300 border-gray-500/30'
                              }`}
                            >
                              {orgRole.role}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Help Text */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-yellow-500/10 border border-yellow-500/20 rounded-full mb-4">
                  <Sparkles className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm text-yellow-300">Need access?</span>
                </div>
                <p className="text-gray-400 max-w-2xl mx-auto">
                  If you believe you should have access to this resource, please contact your
                  organization administrator or try switching to a different organization with the
                  appropriate permissions.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  asChild
                  className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  <Link href="/" className="flex items-center gap-2">
                    <Home className="w-4 h-4 group-hover:rotate-12 transition-transform" />
                    Return to Dashboard
                  </Link>
                </Button>

                <Button
                  asChild
                  variant="outline"
                  className="group border-[#3b3b3b] text-gray-300 hover:bg-white/5 hover:border-white/20 hover:text-white transition-all duration-300"
                >
                  <Link href="/profile" className="flex items-center gap-2">
                    <User className="w-4 h-4 group-hover:scale-110 transition-transform" />
                    View Profile
                  </Link>
                </Button>

                <Button
                  asChild
                  variant="outline"
                  className="group border-[#3b3b3b] text-gray-300 hover:bg-white/5 hover:border-white/20 hover:text-white transition-all duration-300"
                >
                  <Link href="/team" className="flex items-center gap-2">
                    <Settings className="w-4 h-4 group-hover:rotate-90 transition-transform duration-500" />
                    Team Settings
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Custom scrollbar styles */}
      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(139, 92, 246, 0.3);
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(139, 92, 246, 0.5);
        }
      `}</style>
    </div>
  );
}
