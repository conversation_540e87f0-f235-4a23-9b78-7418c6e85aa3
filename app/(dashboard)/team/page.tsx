'use client';

import { UserPlus, UserX, Users, Shield, Activity, RefreshCw, Loader2 } from 'lucide-react';
import { useState, useMemo, useCallback, useTransition } from 'react';
import { toast } from 'sonner';

import { InviteModal } from '@/components/team/invite-modal';
import { RemoveUserButton } from '@/components/team/remove-user-button';
import { ChangeRoleButton } from '@/components/team/change-role-button';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { useOrgContext } from '@/components/org/org-context-provider';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
screenX;

import {
  useOrgUsers,
  useActionLogs,
  useInviteUser,
  useUpdateUserRole,
  useRemoveUser,
} from '@/hooks/use-api';
import type { TeamMember } from '@/types/team';
import { z } from 'zod';
import Link from 'next/link';
import { useDebounce } from '@/hooks/use-debounce';
import { usePagination } from '@/hooks/use-pagination';
import { TeamStats } from '@/components/team/team-stats';
import { TeamMembersTab } from '@/components/team/team-members-tab';
import { RolesTab } from '@/components/team/roles-tab';
import { ActivityLogTab } from '@/components/team/activity-log-tab';

// Role definitions for display purposes
const displayRoles = [
  { name: 'Owner', color: 'from-purple-600 to-pink-600' },
  { name: 'Admin', color: 'from-blue-600 to-purple-600' },
  { name: 'Member', color: 'from-green-600 to-blue-600' },
  { name: 'Viewer', color: 'from-gray-600 to-yellow-600' },
];

// Legacy roles format for components that still need it
const roles = [
  {
    name: 'Owner',
    description: 'Full control over the organization',
    permissions: ['Full administrative control', 'Manage all settings', 'Delete organization'],
  },
  {
    name: 'Admin',
    description: 'Administrative access',
    permissions: ['Manage team members', 'View all data', 'Configure settings'],
  },
  {
    name: 'Member',
    description: 'Standard team member access',
    permissions: ['View devices', 'Submit jobs', 'View own data'],
  },
  {
    name: 'Viewer',
    description: 'Read-only access',
    permissions: ['View devices', 'View analytics', 'View team information'],
  },
];

const actions = [
  'Add New Device',
  'Change Device Info',
  'Delete Device',
  'Invite User',
  'Remove User',
  'Change User Role',
];

const inviteUserSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  role: z.string().min(1, { message: 'Role is required' }),
});

export default function TeamPage() {
  const [isPending, startTransition] = useTransition();

  // Organization context
  const { currentOrgId, currentOrg } = useOrgContext();

  // Dialog state
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionDialogMessage, setActionDialogMessage] = useState('');
  const [actionDialogType, setActionDialogType] = useState<'success' | 'error'>('success');

  // Filter state with debouncing
  const [roleFilter, setRoleFilter] = useState('All Roles');
  const [searchTerm, setSearchTerm] = useState('');
  const [actionLogFilter, setActionLogFilter] = useState('All Actions');
  const [actionLogSearch, setActionLogSearch] = useState('');

  // Debounced search values for better performance
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const debouncedActionLogSearch = useDebounce(actionLogSearch, 300);

  // Modal state
  const [removeUser, setRemoveUser] = useState<TeamMember | null>(null);
  const [changeRoleUser, setChangeRoleUser] = useState<TeamMember | null>(null);
  const [inviteOpen, setInviteOpen] = useState(false);

  // Active tab
  const [activeTab, setActiveTab] = useState('team');

  // Pagination hooks
  const usersPagination = usePagination(0, 10);
  const actionLogPagination = usePagination(0, 15);

  // API hooks - using dynamic orgID from context
  const {
    data: usersData,
    isLoading: usersLoading,
    refetch: refetchUsers,
  } = useOrgUsers(currentOrgId || '', usersPagination.page, usersPagination.pageSize);
  // We can get org info from the context instead of making an extra API call

  const {
    data: actionLogsData,
    isLoading: actionLogsLoading,
    refetch: refetchActionLogs,
  } = useActionLogs('AWS', actionLogPagination.page, actionLogPagination.pageSize);

  // Modern API hooks
  const { mutate: inviteUser, isPending: isInviting } = useInviteUser();
  const { mutate: updateUserRole, isPending: isUpdatingRole } = useUpdateUserRole();
  const { mutate: removeUserMutation, isPending: isRemoving } = useRemoveUser();

  // Extract data from API responses or use sample data
  const users = usersData?.users && usersData.users.length > 0 ? usersData.users : [];
  const totalUsers = usersData?.totalUsers || 0;
  const currentUserRole = usersData?.currentUserRole || null;
  const totalUserPages = Math.ceil(totalUsers / usersPagination.pageSize);
  const actionLogs =
    actionLogsData?.auditLogsArray && actionLogsData.auditLogsArray.length > 0
      ? actionLogsData.auditLogsArray
      : [];
  const totalActionLogPages = Math.max(
    1,
    Math.ceil(actionLogs.length / actionLogPagination.pageSize),
  );

  // Optimized filtered data with memoization
  const filteredUsers = useMemo(
    () =>
      users.filter((member: TeamMember) => {
        const matchesRole =
          roleFilter === 'All Roles' || member.role.toLowerCase() === roleFilter.toLowerCase();
        const matchesSearch =
          member.name?.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          member.email?.toLowerCase().includes(debouncedSearchTerm.toLowerCase());
        return matchesRole && matchesSearch;
      }),
    [users, roleFilter, debouncedSearchTerm],
  );

  const filteredActionLogs = useMemo(
    () =>
      actionLogs.filter((log) => {
        const matchesAction =
          actionLogFilter === 'All Actions' ||
          log.action.toLowerCase() === actionLogFilter.toLowerCase();
        const matchesSearch =
          log.userEmail.toLowerCase().includes(debouncedActionLogSearch.toLowerCase()) ||
          log.actionDescription?.toLowerCase().includes(debouncedActionLogSearch.toLowerCase()) ||
          log.userRole?.toLowerCase().includes(debouncedActionLogSearch.toLowerCase());
        return matchesAction && matchesSearch;
      }),
    [actionLogs, actionLogFilter, debouncedActionLogSearch],
  );

  // Enhanced roles with members and statistics
  const rolesWithMembers = useMemo(
    () =>
      roles.map((role) => {
        const roleMembers = users.filter(
          (member: TeamMember) => member.role.toLowerCase() === role.name.toLowerCase(),
        );
        const displayRole = displayRoles.find((dr) => dr.name === role.name);
        return {
          ...role,
          color: displayRole?.color || 'from-gray-600 to-gray-700',
          members: roleMembers.map((member: TeamMember) => ({
            name: member.name,
            avatar: member.avatar,
            email: member.email,
          })),
          count: roleMembers.length,
        };
      }),
    [users],
  );

  // Statistics for dashboard
  const stats = useMemo(
    () => ({
      totalUsers,
      activeUsers: users.filter((u: TeamMember) => u.status === 'Active').length,
      pendingInvites: users.filter((u: TeamMember) => u.status === 'Invited').length,
      totalRoles: rolesWithMembers.filter((r: any) => r.count > 0).length,
    }),
    [users, totalUsers, rolesWithMembers],
  );

  // Optimized action handlers with better error handling
  const handleInviteUser = useCallback(
    async (values: z.infer<typeof inviteUserSchema>) => {
      if (!currentOrgId) {
        setActionDialogMessage('No organization selected');
        setActionDialogType('error');
        setActionDialogOpen(true);
        return;
      }

      startTransition(async () => {
        try {
          await inviteUser(
            {
              email: values.email,
              role: values.role,
              orgId: currentOrgId,
              orgName: currentOrg?.orgName || '',
            },
            {
              onSuccess: () => {
                setActionDialogMessage(`Successfully invited ${values.email}`);
                setActionDialogType('success');
                refetchUsers();
              },
              onError: (error: any) => {
                setActionDialogMessage(
                  `Failed to invite user: ${error.message || 'Unknown error'}`,
                );
                setActionDialogType('error');
              },
            },
          );
        } catch (error) {
          console.error('Invite error:', error);
          setActionDialogMessage('An unexpected error occurred');
          setActionDialogType('error');
        } finally {
          setActionDialogOpen(true);
        }
      });
    },
    [inviteUser, refetchUsers, currentOrgId, currentOrg],
  );

  const handleRemoveUser = useCallback(async () => {
    if (!removeUser || !currentOrgId) return;

    startTransition(async () => {
      try {
        await removeUserMutation(
          {
            email: removeUser.email,
            orgId: currentOrgId,
            orgName: currentOrg?.orgName || '',
          },
          {
            onSuccess: () => {
              setActionDialogMessage(`Successfully removed ${removeUser.email}`);
              setActionDialogType('success');
              refetchUsers();
            },
            onError: (error: any) => {
              setActionDialogMessage(`Failed to remove user: ${error.message || 'Unknown error'}`);
              setActionDialogType('error');
            },
          },
        );
      } catch (error) {
        console.error('Remove error:', error);
        setActionDialogMessage('An unexpected error occurred');
        setActionDialogType('error');
      } finally {
        setActionDialogOpen(true);
        setRemoveUser(null);
      }
    });
  }, [removeUser, removeUserMutation, currentOrgId, currentOrg, refetchUsers]);

  const handleChangeRole = useCallback(
    async (user: TeamMember, newRole: string) => {
      if (!user || !newRole || !currentOrgId) return;

      startTransition(async () => {
        try {
          await updateUserRole(
            {
              email: user.email,
              role: newRole,
              orgId: currentOrgId,
              orgName: currentOrg?.orgName || '',
            },
            {
              onSuccess: () => {
                setActionDialogMessage(`Successfully changed ${user.email}'s role to ${newRole}`);
                setActionDialogType('success');
                refetchUsers();
              },
              onError: (error: any) => {
                setActionDialogMessage(
                  `Failed to change role: ${error.message || 'Unknown error'}`,
                );
                setActionDialogType('error');
              },
            },
          );
        } catch (error) {
          console.error('Change role error:', error);
          setActionDialogMessage('An unexpected error occurred');
          setActionDialogType('error');
        } finally {
          setActionDialogOpen(true);
          setChangeRoleUser(null);
        }
      });
    },
    [updateUserRole, currentOrgId, currentOrg, refetchUsers],
  );

  const handleResendInvite = useCallback(
    async (email: string) => {
      if (!currentOrgId) {
        toast.error('No organization selected');
        return;
      }

      try {
        // For resend, we use the invite endpoint with the same data
        await inviteUser(
          {
            email,
            role: 'member', // Default role for resend
            orgId: currentOrgId,
            orgName: currentOrg?.orgName || '',
          },
          {
            onSuccess: () => {
              toast.success(`An invite has been resent to ${email}`);
            },
            onError: (error: any) => {
              toast.error(
                `Failed to resend invite to ${email}: ${error.message || 'Unknown error'}`,
              );
            },
          },
        );
      } catch (error: any) {
        toast.error(`Failed to resend invite to ${email}`);
      }
    },
    [inviteUser, currentOrgId, currentOrg],
  );

  const refreshData = useCallback(() => {
    refetchUsers();
    refetchActionLogs();
    toast.success('Data refreshed successfully');
  }, [refetchUsers, refetchActionLogs]);

  // Show loading state if no organization is selected
  if (!currentOrgId) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0f0f0f]">
        <div className="text-center p-8">
          <Loader2 className="w-16 h-16 mx-auto mb-4 animate-spin text-[#8a2be2]" />
          <h2 className="text-xl font-semibold text-white mb-2">Loading Organization...</h2>
          <p className="text-gray-400">Please wait while we load your organization data.</p>
        </div>
      </div>
    );
  }

  return (
    <OrgPermissionGuard
      permission={Permission.ViewTeam}
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-[#0f0f0f]">
          <div className="text-center p-8 bg-white border border-gray-200 rounded-xl shadow-2xl max-w-md">
            <div className="text-red-500 mb-4">
              <UserX className="w-16 h-16 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-6">
              You need team view permissions to access this page.
            </p>
            <Link
              href="/"
              className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              Return to Dashboard
            </Link>
          </div>
        </div>
      }
    >
      {/* Modals */}
      <InviteModal
        open={inviteOpen}
        onClose={() => setInviteOpen(false)}
        onInvite={async (email: string, role: string) => {
          if (!currentOrgId || !currentOrg?.orgName) {
            return {
              message: 'No organization selected or organization name missing',
              currentUserRole: null,
              error: true,
            };
          }
          return new Promise((resolve, reject) => {
            inviteUser(
              {
                email,
                role,
                orgId: currentOrgId,
                orgName: currentOrg.orgName,
              },
              {
                onSuccess: (data) => {
                  // Expect API to return { message, currentUserRole, ... }
                  resolve(data);
                },
                onError: (error) => {
                  resolve({
                    message: error?.message || 'Failed to invite user',
                    currentUserRole: null,
                    error: true,
                  });
                },
              },
            );
          });
        }}
        orgName={currentOrg?.orgName || 'your organization'}
        isLoading={isPending || isInviting}
      />

      {changeRoleUser && (
        <ChangeRoleButton
          user={changeRoleUser}
          roles={roles}
          currentUserRole={currentUserRole}
          onRoleChange={handleChangeRole}
          onClose={() => setChangeRoleUser(null)}
        />
      )}

      {removeUser && (
        <RemoveUserButton
          user={removeUser}
          onRemove={handleRemoveUser}
          onClose={() => setRemoveUser(null)}
        />
      )}

      <div className="min-h-screen bg-[#0f0f0f] overflow-x-auto">
        <main className="p-6 w-full max-w-none">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-4xl font-black bg-gradient-to-r from-white via-gray-100 to-gray-200 bg-clip-text text-transparent mb-3 tracking-tight">
                {currentOrg?.orgName}
              </h1>
              <p className="text-gray-300 text-lg font-medium">
                Team Management - Manage your team members, roles, and permissions
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                onClick={refreshData}
                variant="outline"
                size="sm"
                className="bg-[#262131] border-[#3b3b3b] text-white hover:bg-[#3b3b3b] font-semibold"
                disabled={isPending}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isPending ? 'animate-spin' : ''}`} />
                Refresh
              </Button>

              <OrgPermissionGuard
                permission={Permission.ManageTeam}
                fallback={
                  <Button disabled variant="outline" size="sm" className="opacity-50">
                    <UserPlus className="w-4 h-4 mr-2" />
                    Invite Member
                  </Button>
                }
              >
                <Button
                  onClick={() => setInviteOpen(true)}
                  className="bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a] hover:from-[#9a3bed] hover:to-[#7a2baa] text-white shadow-lg font-semibold"
                  disabled={isPending}
                >
                  <UserPlus className="w-4 h-4 mr-2" />
                  Invite Member
                </Button>
              </OrgPermissionGuard>
            </div>
          </div>

          {/* Statistics Cards */}
          <TeamStats stats={stats} isLoading={usersLoading} />

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 bg-[#262131] border border-[#3b3b3b] p-1">
              <TabsTrigger
                value="team"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#8a2be2] data-[state=active]:to-[#6a1b9a] data-[state=active]:text-white data-[state=active]:font-bold text-gray-300 font-semibold transition-all duration-200"
              >
                <Users className="w-5 h-5 mr-2" />
                Team Members
                <Badge variant="secondary" className="ml-2 bg-[#3b3b3b] text-white font-medium">
                  {totalUsers}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="roles"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#8a2be2] data-[state=active]:to-[#6a1b9a] data-[state=active]:text-white data-[state=active]:font-bold text-gray-300 font-semibold transition-all duration-200"
              >
                <Shield className="w-5 h-5 mr-2" />
                Roles & Permissions
                <Badge variant="secondary" className="ml-2 bg-[#3b3b3b] text-white font-medium">
                  {stats.totalRoles}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="activity"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#8a2be2] data-[state=active]:to-[#6a1b9a] data-[state=active]:text-white data-[state=active]:font-bold text-gray-300 font-semibold transition-all duration-200"
              >
                <Activity className="w-5 h-5 mr-2" />
                Activity Log
                <Badge variant="secondary" className="ml-2 bg-[#3b3b3b] text-white font-medium">
                  {actionLogs.length}
                </Badge>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="team" className="space-y-6">
              <TeamMembersTab
                filteredUsers={filteredUsers}
                usersLoading={usersLoading}
                totalUserPages={totalUserPages}
                pagination={usersPagination}
                roleFilter={roleFilter}
                setRoleFilter={setRoleFilter}
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                roles={roles}
                totalUsers={totalUsers}
                onChangeRole={setChangeRoleUser}
                onRemove={setRemoveUser}
                currentUserRole={currentUserRole}
              />
            </TabsContent>

            <TabsContent value="roles" className="space-y-6">
              <RolesTab rolesWithMembers={rolesWithMembers} />
            </TabsContent>

            <TabsContent value="activity" className="space-y-6">
              <ActivityLogTab
                filteredActionLogs={filteredActionLogs}
                actionLogsLoading={actionLogsLoading}
                totalActionLogPages={totalActionLogPages}
                pagination={actionLogPagination}
                actionLogFilter={actionLogFilter}
                setActionLogFilter={setActionLogFilter}
                actionLogSearch={actionLogSearch}
                setActionLogSearch={setActionLogSearch}
                actions={actions}
                totalLogs={actionLogs.length}
              />
            </TabsContent>
          </Tabs>
        </main>
      </div>

      {/* Success/Error Dialog */}
      <Dialog open={actionDialogOpen} onOpenChange={setActionDialogOpen}>
        <DialogContent className="text-center bg-[#262131] border-[#3b3b3b]">
          <DialogTitle className="text-white text-2xl font-bold">
            {actionDialogType === 'success' ? '✅ Success' : '❌ Error'}
          </DialogTitle>
          <div
            className={`mt-4 p-5 rounded-lg ${actionDialogType === 'success' ? 'bg-green-500/10 text-green-300 border border-green-500/30' : 'bg-red-500/10 text-red-300 border border-red-500/30'}`}
          >
            <p className="text-base font-medium">{actionDialogMessage}</p>
          </div>
          <Button
            className="mt-6 bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a] hover:from-[#9a3bed] hover:to-[#7a2baa] text-white font-semibold"
            onClick={() => setActionDialogOpen(false)}
          >
            Close
          </Button>
        </DialogContent>
      </Dialog>
    </OrgPermissionGuard>
  );
}
