'use client';

import { useState, useEffect } from 'react';
import { Loader2, PlusIcon } from 'lucide-react';
import Link from 'next/link';
import { useAllDevices } from '@/hooks/use-api';
import { DeviceOverviewCard } from '@/components/devices/device-overview-card';
import { DevicesSection } from '@/components/devices/devices-section';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { useSearchParams } from 'next/navigation';

export default function DevicesPage() {
  const { data: devices, isLoading } = useAllDevices();
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const queryId = searchParams.get('id');

  useEffect(() => {
    if (queryId) {
      setSelectedDeviceId(queryId);
    } else if (!selectedDeviceId && devices && devices.length > 0) {
      setSelectedDeviceId(devices[0].qbraid_id);
    }
  }, [devices, queryId, selectedDeviceId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="size-8 animate-spin text-[#8a2be2]" />
      </div>
    );
  }

  if (!devices || devices.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen text-muted-foreground">
        No devices found
      </div>
    );
  }

  const selectedDevice = devices.find((d) => d.qbraid_id === selectedDeviceId) || devices[0];

  return (
    <OrgPermissionGuard
      permission={Permission.ViewDevices}
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center p-8 bg-red-50 border border-red-200 rounded-lg max-w-md">
            <h2 className="text-xl font-semibold text-red-800 mb-2">Access Denied</h2>
            <p className="text-red-600 mb-4">
              You need device view permissions to access this page.
            </p>
            <Link href="/" className="text-blue-600 hover:text-blue-800 underline">
              Return to Dashboard
            </Link>
          </div>
        </div>
      }
    >
      <main className="max-w-[1400px] mx-auto px-6 py-8">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h1 className="text-3xl font-semibold text-white">Devices</h1>
          <OrgPermissionGuard permission={Permission.ManageDevices} fallback={null}>
            <Button
              variant="default"
              className="bg-[#32164b] hover:bg-[#7c2dd5] text-white"
              asChild
            >
              <Link href="/add-device" className="flex items-center gap-2">
                <PlusIcon className="size-4" /> Add Device
              </Link>
            </Button>
          </OrgPermissionGuard>
        </div>

        {/* Overview cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 py-4">
          {devices.map((device) => (
            <div
              key={device.qbraid_id}
              onClick={() => setSelectedDeviceId(device.qbraid_id)}
              className={`cursor-pointer transition-transform duration-300 ${selectedDeviceId === device.qbraid_id ? 'scale-[1.04]' : 'opacity-80 hover:opacity-100 hover:-translate-y-1'}`}
            >
              <DeviceOverviewCard {...device} selected={selectedDeviceId === device.qbraid_id} />
            </div>
          ))}
        </div>

        {/* Device details section */}
        <div className="mt-8">
          <DevicesSection device={selectedDevice} />
        </div>
      </main>
    </OrgPermissionGuard>
  );
}
