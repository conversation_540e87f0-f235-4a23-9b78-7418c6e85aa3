'use client';

import { useState, useMemo, useCallback, useTransition, useEffect } from 'react';
import { toast } from 'sonner';
import {
  User,
  Mail,
  Building2,
  Shield,
  Settings,
  AlertCircle,
  CheckCircle,
  Edit3,
  Save,
  X,
  Clock,
  Users,
  RefreshCw,
  Bell,
  Eye,
  EyeOff,
  UserPlus,
  Phone,
  MapPin,
  Briefcase,
  Calendar,
  Award,
  Key,
  Book,
  Code,
} from 'lucide-react';
import Link from 'next/link';

import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { useOrgPermissions } from '@/hooks/use-permissions';
import { useOrgContext } from '@/components/org/org-context-provider';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { LoaderTwo } from '@/components/ui/loader';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  useUserProfile,
  useUpdateProfile,
  useUserOrganizations,
  useAcceptInvitation,
  useDeclineInvitation,
} from '@/hooks/use-api';
import type { UserProfile } from '@/types/user';
import { getRoleDisplayName } from '@/lib/permissions';

// TypeScript interfaces
interface Organization {
  orgId: string;
  orgName: string;
  role: string;
  accepted: boolean;
  invited: boolean;
  description?: string;
  email?: string;
  credits?: number;
  devices?: any[];
  joinedAt?: string;
}

interface UserSession {
  email: string;
  sessionId: string;
  timestamp: string;
}

// Helper function to format relative time
const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) return 'Just now';
  if (diffInHours < 24) return `${diffInHours}h ago`;
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d ago`;
  return date.toLocaleDateString();
};

// Helper function to check if invitation is expiring soon
const isExpiringSoon = (expiresAt: string) => {
  const expiry = new Date(expiresAt);
  const now = new Date();
  const diffInDays = Math.floor((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  return diffInDays <= 3;
};

// Sidebar navigation items
const sidebarItems = [
  {
    id: 'profile',
    label: 'Personal Info',
    icon: User,
    description: 'Manage your personal details',
  },
  {
    id: 'organizations',
    label: 'Organizations',
    icon: Building2,
    description: 'Your organization memberships',
  },
  {
    id: 'invitations',
    label: 'Invitations',
    icon: UserPlus,
    description: 'Pending organization invites',
  },
  { id: 'security', label: 'Security', icon: Shield, description: 'Account security settings' },
  {
    id: 'preferences',
    label: 'Preferences',
    icon: Settings,
    description: 'Notification and app settings',
  },
];

export default function ProfilePage() {
  const [isPending, startTransition] = useTransition();
  const [activeSection, setActiveSection] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [settings, setSettings] = useState({
    emailNotifications: true,
    organizationUpdates: true,
    securityAlerts: true,
    marketingEmails: false,
    twoFactorAuth: false,
  });
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionDialogMessage, setActionDialogMessage] = useState('');
  const [actionDialogType, setActionDialogType] = useState<'success' | 'error'>('success');

  const {
    roles,
    permissions,
    isLoading: permissionsLoading,
    refreshPermissions,
  } = useOrgPermissions();

  // Fetch user profile data
  const {
    data: profile,
    isLoading: profileLoading,
    error: profileError,
    refetch: refetchProfile,
  } = useUserProfile();

  // Fetch user organizations
  const {
    data: orgsData,
    isLoading: orgsLoading,
    error: orgsError,
    refetch: refetchOrganizations,
  } = useUserOrganizations(0, 50); // Fetch up to 50 organizations

  const { mutate: updateProfile } = useUpdateProfile();
  const { mutate: acceptInvitation } = useAcceptInvitation();
  const { mutate: declineInvitation } = useDeclineInvitation();

  // Extract organizations from the response
  const userOrganizations = useMemo(() => {
    return orgsData?.organizations || [];
  }, [orgsData]);

  const [formData, setFormData] = useState<Partial<UserProfile>>({});

  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        personalInformation: { ...profile.personalInformation },
      });
    }
  }, [profile]);

  // Debug organizations error
  useEffect(() => {
    if (orgsError) {
      console.error('❌ [PROFILE] Organizations error:', orgsError);
    }
  }, [orgsError]);

  const handleInputChange = (field: keyof UserProfile, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handlePersonalInfoChange = (
    subfield: keyof NonNullable<UserProfile['personalInformation']>,
    value: any,
  ) => {
    setFormData((prev) => ({
      ...prev,
      personalInformation: {
        ...(prev.personalInformation || {}),
        [subfield]: value,
      },
    }));
  };

  const handleSave = useCallback(async () => {
    startTransition(async () => {
      updateProfile(formData, {
        onSuccess: () => {
          setActionDialogMessage('Profile updated successfully');
          setActionDialogType('success');
          setIsEditing(false);
          refetchProfile();
          setActionDialogOpen(true);
        },
        onError: (error: any) => {
          setActionDialogMessage('Failed to update profile');
          setActionDialogType('error');
          setActionDialogOpen(true);
        },
      });
    });
  }, [formData, updateProfile, refetchProfile, startTransition]);

  const handleCancel = useCallback(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        personalInformation: { ...profile.personalInformation },
      });
    }
    setIsEditing(false);
  }, [profile]);

  const refreshData = useCallback(() => {
    refreshPermissions();
    refetchProfile();
    refetchOrganizations();
    toast.success('Data refreshed successfully');
  }, [refreshPermissions, refetchProfile, refetchOrganizations]);

  // Invitation handlers
  const handleAcceptInvitation = useCallback(
    async (invitationId: string, orgName: string) => {
      if (!profile?.email) {
        setActionDialogMessage('Profile email not available');
        setActionDialogType('error');
        setActionDialogOpen(true);
        return;
      }

      const userEmail = profile.email;
      acceptInvitation(
        { orgId: invitationId, email: userEmail },
        {
          onSuccess: () => {
            setActionDialogMessage(`Successfully joined ${orgName}`);
            setActionDialogType('success');
            refreshPermissions();
            refetchOrganizations();
            setActionDialogOpen(true);
          },
          onError: (error: any) => {
            setActionDialogMessage(`Failed to join ${orgName}`);
            setActionDialogType('error');
            setActionDialogOpen(true);
          },
        },
      );
    },
    [profile?.email, acceptInvitation, refreshPermissions, refetchOrganizations],
  );

  const handleDeclineInvitation = useCallback(
    async (invitationId: string, orgName: string) => {
      if (!profile?.email) {
        setActionDialogMessage('Profile email not available');
        setActionDialogType('error');
        setActionDialogOpen(true);
        return;
      }

      const userEmail = profile.email;
      declineInvitation(
        { orgName, email: userEmail },
        {
          onSuccess: () => {
            setActionDialogMessage(`Declined invitation to ${orgName}`);
            setActionDialogType('success');
            refetchOrganizations();
            setActionDialogOpen(true);
          },
          onError: (error: any) => {
            setActionDialogMessage(`Failed to decline invitation`);
            setActionDialogType('error');
            setActionDialogOpen(true);
          },
        },
      );
    },
    [profile?.email, declineInvitation, refetchOrganizations],
  );

  // Settings handlers
  const handleSettingsChange = useCallback((key: string, value: boolean) => {
    setSettings((prev) => ({ ...prev, [key]: value }));
  }, []);

  const handleSaveSettings = useCallback(async () => {
    startTransition(async () => {
      try {
        // TODO: Implement settings update API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setActionDialogMessage('Settings updated successfully');
        setActionDialogType('success');
      } catch (error) {
        setActionDialogMessage('Failed to update settings');
        setActionDialogType('error');
      } finally {
        setActionDialogOpen(true);
      }
    });
  }, [settings, startTransition]);

  const isLoading = profileLoading || permissionsLoading || orgsLoading;

  const stats = useMemo(
    () => ({
      totalOrganizations: userOrganizations.filter((org: Organization) => org.accepted).length,
      pendingInvitations: userOrganizations.filter(
        (org: Organization) => !org.accepted && org.invited,
      ).length,
      totalRoles: roles.length,
      activePermissions: permissions.length,
    }),
    [userOrganizations, roles.length, permissions.length],
  );

  // Get user initials for avatar
  const getUserInitials = (name: string, email: string) => {
    if (name) {
      return name
        .split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return 'U';
  };

  return (
    <OrgPermissionGuard
      permission={Permission.ViewProfile}
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-[#0f0f0f]">
          <div className="text-center p-8 bg-white border border-gray-200 rounded-xl shadow-2xl max-w-md">
            <div className="text-red-500 mb-4">
              <AlertCircle className="w-16 h-16 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-6">
              You need profile view permissions to access this page.
            </p>
            <Link
              href="/"
              className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              Return to Dashboard
            </Link>
          </div>
        </div>
      }
    >
      <div className="min-h-screen bg-[#0f0f0f]">
        {/* Profile Completion Banner */}
        {(!formData.firstName || !formData.lastName) && !profileLoading && (
          <div className="bg-gradient-to-r from-orange-600/20 to-yellow-600/20 border-b border-orange-500/30 px-6 py-3">
            <div className="max-w-7xl mx-auto flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-orange-400" />
                <p className="text-orange-100 text-sm font-medium">
                  Complete your profile to personalize your experience
                </p>
              </div>
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                Complete Profile
              </Button>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="bg-gradient-to-r from-[#8a2be2] via-[#6a1b9a] to-[#8a2be2] px-6 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <Avatar className="h-24 w-24 border-4 border-white/20">
                  <AvatarImage src="/placeholder-user.jpg" alt={formData.firstName} />
                  <AvatarFallback>
                    {getUserInitials(
                      `${formData.firstName} ${formData.lastName}`,
                      profile?.email || '',
                    )}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h1 className="text-3xl font-bold text-white mb-1">
                    {profileLoading ? (
                      <Skeleton className="h-8 w-48 bg-white/20" />
                    ) : (
                      (() => {
                        const firstName = formData.firstName || profile?.firstName || '';
                        const lastName = formData.lastName || profile?.lastName || '';
                        const fullName = `${firstName} ${lastName}`.trim();

                        // If no name is available, show email username
                        if (!fullName && profile?.email) {
                          const emailUsername = profile.email.split('@')[0];
                          return emailUsername.charAt(0).toUpperCase() + emailUsername.slice(1);
                        }

                        return fullName || 'User';
                      })()
                    )}
                  </h1>
                  <div className="text-purple-100 mb-2">
                    {profileLoading ? (
                      <Skeleton className="h-5 w-64 bg-white/20" />
                    ) : (
                      profile?.email
                    )}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-purple-100">
                    <span className="flex items-center">
                      <Briefcase className="w-4 h-4 mr-1" />
                      {profile?.personalInformation?.workHistory || 'Software Engineer'}
                    </span>
                    <span className="flex items-center">
                      <Building2 className="w-4 h-4 mr-1" />
                      {stats.totalOrganizations} Organizations
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  onClick={refreshData}
                  variant="outline"
                  size="sm"
                  className="border-white/20 text-white hover:bg-white/10"
                  disabled={isPending || isLoading}
                >
                  <RefreshCw
                    className={`w-4 h-4 mr-2 ${isPending || isLoading ? 'animate-spin' : ''}`}
                  />
                  Refresh
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto p-6">
          <div className="grid grid-cols-12 gap-6">
            {/* Sidebar */}
            <div className="col-span-12 lg:col-span-3">
              <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">Account Settings</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <nav className="space-y-1">
                    {sidebarItems.map((item) => {
                      const Icon = item.icon;
                      const isActive = activeSection === item.id;
                      return (
                        <button
                          key={item.id}
                          onClick={() => setActiveSection(item.id)}
                          className={`
                            w-full text-left px-4 py-3 rounded-lg transition-all duration-200 group
                            ${
                              isActive
                                ? 'bg-gradient-to-r from-[#8a2be2]/20 to-[#6a1b9a]/20 border-l-4 border-[#8a2be2] text-purple-300'
                                : 'text-[#94a3b8] hover:bg-[#3b3b3b]/50 hover:text-white'
                            }
                          `}
                        >
                          <div className="flex items-center space-x-3">
                            <Icon
                              className={`w-5 h-5 ${isActive ? 'text-[#8a2be2]' : 'text-[#94a3b8] group-hover:text-white'}`}
                            />
                            <div>
                              <div className="font-medium">{item.label}</div>
                              <div className="text-xs text-[#94a3b8] group-hover:text-white">
                                {item.description}
                              </div>
                            </div>
                          </div>
                        </button>
                      );
                    })}
                  </nav>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm mt-6">
                <CardHeader>
                  <CardTitle className="text-white text-sm font-medium">Quick Overview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-[#94a3b8] text-sm">Organizations</span>
                    <Badge variant="secondary" className="bg-[#8a2be2]/20 text-purple-300">
                      {stats.totalOrganizations}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-[#94a3b8] text-sm">Pending Invites</span>
                    <Badge variant="secondary" className="bg-orange-500/20 text-orange-300">
                      {stats.pendingInvitations}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-[#94a3b8] text-sm">Active Roles</span>
                    <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                      {stats.totalRoles}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-[#94a3b8] text-sm">Permissions</span>
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      {stats.activePermissions}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main */}
            <div className="col-span-12 lg:col-span-9">
              {activeSection === 'profile' && (
                <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle className="text-white flex items-center">
                        <User className="w-5 h-5 mr-2 text-[#8a2be2]" />
                        Personal Information
                      </CardTitle>
                      <p className="text-[#94a3b8] text-sm mt-1">
                        Manage your personal details and contact information
                      </p>
                    </div>
                    {!isEditing ? (
                      <Button
                        onClick={() => setIsEditing(true)}
                        className="bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a] hover:from-[#9a3bed] hover:to-[#7a2baa] text-white"
                      >
                        <Edit3 className="w-4 h-4 mr-2" />
                        Edit Profile
                      </Button>
                    ) : (
                      <div className="flex space-x-2">
                        <Button
                          onClick={handleSave}
                          disabled={isPending}
                          className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          {isPending ? 'Saving...' : 'Save'}
                        </Button>
                        <Button
                          onClick={handleCancel}
                          variant="outline"
                          className="border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b]"
                        >
                          <X className="w-4 h-4 mr-2" />
                          Cancel
                        </Button>
                      </div>
                    )}
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {profileLoading ? (
                      <div className="flex justify-center items-center py-12">
                        <LoaderTwo />
                      </div>
                    ) : profileError ? (
                      <div className="text-red-400">Error loading profile</div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <Label htmlFor="firstName" className="text-white">
                            First Name
                          </Label>
                          {isEditing ? (
                            <Input
                              id="firstName"
                              value={formData.firstName || ''}
                              onChange={(e) => handleInputChange('firstName', e.target.value)}
                              className="mt-2 bg-[#1a1a2e] border-[#3b3b3b] text-white placeholder:text-[#94a3b8] focus:ring-2 focus:ring-[#8a2be2] focus:border-transparent"
                              placeholder="Enter your first name"
                            />
                          ) : (
                            <div className="mt-2 text-white">
                              {formData.firstName || (
                                <span className="text-[#94a3b8] italic">
                                  Click Edit Profile to add
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                        <div>
                          <Label htmlFor="lastName" className="text-white">
                            Last Name
                          </Label>
                          {isEditing ? (
                            <Input
                              id="lastName"
                              value={formData.lastName || ''}
                              onChange={(e) => handleInputChange('lastName', e.target.value)}
                              className="mt-2 bg-[#1a1a2e] border-[#3b3b3b] text-white placeholder:text-[#94a3b8] focus:ring-2 focus:ring-[#8a2be2] focus:border-transparent"
                              placeholder="Enter your last name"
                            />
                          ) : (
                            <div className="mt-2 text-white">
                              {formData.lastName || (
                                <span className="text-[#94a3b8] italic">
                                  Click Edit Profile to add
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="email" className="text-white">
                            Email
                          </Label>
                          <div className="mt-2 text-white">{profile?.email || 'Not provided'}</div>
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="bio" className="text-white">
                            Bio
                          </Label>
                          {isEditing ? (
                            <Input
                              id="bio"
                              value={formData.personalInformation?.bio || ''}
                              onChange={(e) => handlePersonalInfoChange('bio', e.target.value)}
                              className="mt-2 bg-[#1a1a2e] border-[#3b3b3b] text-white placeholder:text-[#94a3b8] focus:ring-2 focus:ring-[#8a2be2] focus:border-transparent"
                            />
                          ) : (
                            <div className="mt-2 text-white">
                              {formData.personalInformation?.bio || 'Not provided'}
                            </div>
                          )}
                        </div>
                        {/* Add more fields like linkedIn, githubUrl, etc. */}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Quantum Section */}
              {activeSection === 'quantum' && (
                <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Key className="w-5 h-5 mr-2 text-[#8a2be2]" />
                      Quantum Jobs
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {profileLoading ? (
                      <Skeleton className="h-32 w-full bg-[#3b3b3b]" />
                    ) : profile?.quantum?.quantumJobs?.length ? (
                      profile.quantum.quantumJobs.map((job, index) => (
                        <div key={index} className="p-2 border-b border-[#3b3b3b]">
                          {/* Display job details */}
                          Job {index + 1}
                        </div>
                      ))
                    ) : (
                      <div className="text-[#94a3b8]">No quantum jobs yet.</div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Learn Section */}
              {activeSection === 'learn' && (
                <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Book className="w-5 h-5 mr-2 text-[#8a2be2]" />
                      Learning Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {profileLoading ? (
                      <Skeleton className="h-32 w-full bg-[#3b3b3b]" />
                    ) : (
                      <>
                        <h4 className="text-white mb-2">Registered Courses</h4>
                        {profile?.learn?.registeredCourses?.length ? (
                          profile.learn.registeredCourses.map((course, index) => (
                            <div key={index} className="p-2 border-b border-[#3b3b3b]">
                              Course ID: {course.$oid}
                            </div>
                          ))
                        ) : (
                          <div className="text-[#94a3b8]">No registered courses.</div>
                        )}
                      </>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Lab Section */}
              {activeSection === 'lab' && (
                <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Code className="w-5 h-5 mr-2 text-[#8a2be2]" />
                      Lab Environments
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {profileLoading ? (
                      <Skeleton className="h-32 w-full bg-[#3b3b3b]" />
                    ) : (
                      <>
                        <h4 className="text-white mb-2">Environments</h4>
                        {profile?.lab?.environments?.length ? (
                          profile.lab.environments.map((env, index) => (
                            <div key={index} className="p-2 border-b border-[#3b3b3b]">
                              Environment {index + 1}
                            </div>
                          ))
                        ) : (
                          <div className="text-[#94a3b8]">No lab environments.</div>
                        )}
                      </>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Organizations - use context */}
              {activeSection === 'organizations' && (
                <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Building2 className="w-5 h-5 mr-2 text-[#8a2be2]" />
                      Organizations
                    </CardTitle>
                    <p className="text-[#94a3b8] text-sm">Organizations you're a member of</p>
                  </CardHeader>
                  <CardContent>
                    {isLoading ? (
                      <div className="flex justify-center items-center py-12">
                        <LoaderTwo />
                      </div>
                    ) : userOrganizations.filter((org: Organization) => org.accepted).length ===
                      0 ? (
                      <div className="text-center py-12">
                        <Building2 className="w-12 h-12 mx-auto text-[#94a3b8] mb-4" />
                        <h3 className="text-white font-medium mb-2">No Organizations</h3>
                        <div className="text-[#94a3b8] text-sm">
                          You're not a member of any organizations yet.
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {userOrganizations
                          .filter((org: Organization) => org.accepted)
                          .map((org: Organization) => (
                            <div
                              key={org.orgId}
                              className="p-4 bg-gradient-to-r from-[#1a1a2e] to-[#262131] rounded-lg border border-[#3b3b3b] hover:border-[#8a2be2]/50 transition-all duration-200"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a] flex items-center justify-center">
                                    <span className="text-white font-semibold text-lg">
                                      {org.orgName.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <div>
                                    <h3 className="text-white font-medium">{org.orgName}</h3>
                                    <div className="flex items-center space-x-2 mt-1">
                                      <Badge className="bg-[#8a2be2]/20 text-purple-300 border-[#8a2be2]/30">
                                        {org.role}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="text-[#94a3b8] hover:text-white"
                                      >
                                        <Settings className="w-4 h-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent className="bg-[#262131] border-[#3b3b3b]">
                                      <DropdownMenuItem className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]">
                                        View Details
                                      </DropdownMenuItem>
                                      <DropdownMenuItem className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]">
                                        Manage Roles
                                      </DropdownMenuItem>
                                      <DropdownMenuItem className="text-red-400 hover:text-red-300 hover:bg-[#3b3b3b]">
                                        Leave Organization
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Invitations Section */}
              {activeSection === 'invitations' && (
                <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <UserPlus className="w-5 h-5 mr-2 text-[#8a2be2]" />
                      Pending Invitations
                    </CardTitle>
                    <p className="text-[#94a3b8] text-sm">
                      Organization invitations waiting for your response
                    </p>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const pendingInvitations = userOrganizations.filter(
                        (org: Organization) => !org.accepted && org.invited,
                      );

                      if (pendingInvitations.length === 0) {
                        return (
                          <div className="text-center py-12">
                            <UserPlus className="w-12 h-12 mx-auto text-[#94a3b8] mb-4" />
                            <h3 className="text-white font-medium mb-2">No Pending Invitations</h3>
                            <div className="text-[#94a3b8] text-sm">
                              You're all caught up! No organization invitations at the moment.
                            </div>
                          </div>
                        );
                      }

                      return (
                        <div className="space-y-4">
                          {pendingInvitations.map((org: Organization) => (
                            <div
                              key={org.orgId}
                              className="p-4 bg-gradient-to-r from-orange-500/10 to-yellow-500/10 rounded-lg border border-orange-500/30"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-orange-500 to-yellow-600 flex items-center justify-center">
                                    <span className="text-white font-semibold text-lg">
                                      {org.orgName.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <div>
                                    <h3 className="text-white font-medium">{org.orgName}</h3>
                                    <div className="flex items-center space-x-4 mt-1">
                                      <Badge className="bg-orange-500/20 text-orange-300 border-orange-500/30">
                                        {getRoleDisplayName(org.role)}
                                      </Badge>
                                      {org.description && (
                                        <span className="text-[#94a3b8] text-sm">
                                          {org.description}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    onClick={() => handleAcceptInvitation(org.orgId, org.orgName)}
                                    disabled={isPending}
                                    className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white"
                                  >
                                    <CheckCircle className="w-4 h-4 mr-2" />
                                    Accept
                                  </Button>
                                  <Button
                                    onClick={() => handleDeclineInvitation(org.orgId, org.orgName)}
                                    disabled={isPending}
                                    variant="outline"
                                    className="border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b]"
                                  >
                                    <X className="w-4 h-4 mr-2" />
                                    Decline
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      );
                    })()}
                  </CardContent>
                </Card>
              )}

              {/* Security Section */}
              {activeSection === 'security' && (
                <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Shield className="w-5 h-5 mr-2 text-[#8a2be2]" />
                      Security Settings
                    </CardTitle>
                    <p className="text-[#94a3b8] text-sm">
                      Manage your account security and authentication
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-[#1a1a2e] rounded-lg border border-[#3b3b3b]">
                        <div className="flex items-center space-x-3">
                          <Key className="w-5 h-5 text-[#8a2be2]" />
                          <div>
                            <h3 className="text-white font-medium">Two-Factor Authentication</h3>
                            <p className="text-[#94a3b8] text-sm">
                              Add an extra layer of security to your account
                            </p>
                          </div>
                        </div>
                        <Button
                          onClick={() =>
                            handleSettingsChange('twoFactorAuth', !settings.twoFactorAuth)
                          }
                          className={
                            settings.twoFactorAuth
                              ? 'bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700'
                              : 'bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a] hover:from-[#9a3bed] hover:to-[#7a2baa]'
                          }
                        >
                          {settings.twoFactorAuth ? 'Enabled' : 'Enable'}
                        </Button>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-[#1a1a2e] rounded-lg border border-[#3b3b3b]">
                        <div className="flex items-center space-x-3">
                          <Shield className="w-5 h-5 text-[#8a2be2]" />
                          <div>
                            <h3 className="text-white font-medium">Password</h3>
                            <p className="text-[#94a3b8] text-sm">Last changed 3 months ago</p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          className="border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b]"
                        >
                          Change Password
                        </Button>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-[#1a1a2e] rounded-lg border border-[#3b3b3b]">
                        <div className="flex items-center space-x-3">
                          <Calendar className="w-5 h-5 text-[#8a2be2]" />
                          <div>
                            <h3 className="text-white font-medium">Active Sessions</h3>
                            <p className="text-[#94a3b8] text-sm">
                              Manage your active login sessions
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          className="border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b]"
                        >
                          View Sessions
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Preferences Section */}
              {activeSection === 'preferences' && (
                <Card className="bg-[#262131] border-[#3b3b3b] backdrop-blur-sm">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle className="text-white flex items-center">
                        <Settings className="w-5 h-5 mr-2 text-[#8a2be2]" />
                        Preferences
                      </CardTitle>
                      <p className="text-[#94a3b8] text-sm mt-1">
                        Customize your notification and app preferences
                      </p>
                    </div>
                    <Button
                      onClick={handleSaveSettings}
                      disabled={isPending}
                      className="bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a] hover:from-[#9a3bed] hover:to-[#7a2baa] text-white"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      {isPending ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-white font-medium flex items-center">
                        <Bell className="w-4 h-4 mr-2 text-[#8a2be2]" />
                        Notification Preferences
                      </h3>

                      {[
                        {
                          key: 'emailNotifications',
                          label: 'Email Notifications',
                          description: 'Receive notifications via email',
                        },
                        {
                          key: 'organizationUpdates',
                          label: 'Organization Updates',
                          description: 'Get notified about organization changes',
                        },
                        {
                          key: 'securityAlerts',
                          label: 'Security Alerts',
                          description: 'Important security-related notifications',
                        },
                        {
                          key: 'marketingEmails',
                          label: 'Marketing Emails',
                          description: 'Product updates and promotional content',
                        },
                      ].map((setting) => (
                        <div
                          key={setting.key}
                          className="flex items-center justify-between p-4 bg-[#1a1a2e] rounded-lg border border-[#3b3b3b]"
                        >
                          <div>
                            <h4 className="text-white font-medium">{setting.label}</h4>
                            <p className="text-[#94a3b8] text-sm">{setting.description}</p>
                          </div>
                          <Button
                            onClick={() =>
                              handleSettingsChange(setting.key, !(settings as any)[setting.key])
                            }
                            variant={(settings as any)[setting.key] ? 'default' : 'outline'}
                            size="sm"
                            className={
                              (settings as any)[setting.key]
                                ? 'bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700'
                                : 'border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b]'
                            }
                          >
                            {(settings as any)[setting.key] ? (
                              <Eye className="w-4 h-4" />
                            ) : (
                              <EyeOff className="w-4 h-4" />
                            )}
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>

        {/* Dialog */}
        <Dialog open={actionDialogOpen} onOpenChange={setActionDialogOpen}>
          <DialogContent className="bg-[#262131] border-[#3b3b3b]">
            <DialogTitle className="text-white flex items-center">
              {actionDialogType === 'success' ? (
                <CheckCircle className="w-5 h-5 mr-2 text-emerald-400" />
              ) : (
                <AlertCircle className="w-5 h-5 mr-2 text-red-400" />
              )}
              {actionDialogType === 'success' ? 'Success' : 'Error'}
            </DialogTitle>
            <DialogDescription className="text-[#94a3b8]">{actionDialogMessage}</DialogDescription>
            <div className="flex justify-end mt-4">
              <Button
                onClick={() => setActionDialogOpen(false)}
                className="bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a] hover:from-[#9a3bed] hover:to-[#7a2baa] text-white"
              >
                OK
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </OrgPermissionGuard>
  );
}
