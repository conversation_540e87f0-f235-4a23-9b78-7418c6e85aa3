'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { CheckCircle, AlertCircle, Mail, ExternalLink, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface InviteStatus {
  status: 'loading' | 'success' | 'error' | 'invalid';
  message: string;
  orgName?: string;
}

function AcceptInviteContent() {
  const [inviteStatus, setInviteStatus] = useState<InviteStatus>({
    status: 'loading',
    message: 'Processing invitation...',
  });
  const searchParams = useSearchParams();
  const router = useRouter();

  // Extract parameters from URL
  const orgId = searchParams.get('orgid');
  const email = searchParams.get('email');
  const invite = searchParams.get('invite');
  const orgName = searchParams.get('orgname') || 'the organization';

  useEffect(() => {
    const processInvite = async () => {
      // Validate required parameters
      if (!orgId || !email || invite !== 'true') {
        setInviteStatus({
          status: 'invalid',
          message: 'Invalid invitation link. Missing required parameters.',
        });
        return;
      }

      try {
        console.log('🎯 [ACCEPT-INVITE] Processing invitation:', {
          orgId,
          email,
          orgName,
        });

        // Call API to accept the invitation
        const response = await fetch('/api/orgs/acceptOrganizationInvite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orgId,
            email,
            orgName,
          }),
        });

        const result = await response.json();

        if (!response.ok || !result.success) {
          throw new Error(result.message || 'Failed to accept invitation');
        }

        setInviteStatus({
          status: 'success',
          message: result.message || `Successfully joined ${orgName}!`,
          orgName: result.orgName || orgName,
        });

        // Redirect to dashboard after 3 seconds
        setTimeout(() => {
          router.push('/dashboard');
          router.refresh();
        }, 3000);
      } catch (error) {
        console.error('❌ [ACCEPT-INVITE] Error:', error);
        setInviteStatus({
          status: 'error',
          message: error instanceof Error ? error.message : 'Failed to accept invitation',
        });
      }
    };

    processInvite();
  }, [orgId, email, invite, orgName, router]);

  const getStatusIcon = () => {
    switch (inviteStatus.status) {
      case 'loading':
        return <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-8 h-8 text-green-500" />;
      case 'error':
      case 'invalid':
        return <AlertCircle className="w-8 h-8 text-red-500" />;
      default:
        return <Mail className="w-8 h-8 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (inviteStatus.status) {
      case 'loading':
        return 'border-blue-200 bg-blue-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
      case 'invalid':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className={`${getStatusColor()} shadow-xl`}>
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">{getStatusIcon()}</div>
            <CardTitle className="text-2xl font-bold">
              {inviteStatus.status === 'loading' && 'Processing Invitation'}
              {inviteStatus.status === 'success' && 'Welcome!'}
              {inviteStatus.status === 'error' && 'Invitation Failed'}
              {inviteStatus.status === 'invalid' && 'Invalid Invitation'}
            </CardTitle>
            <CardDescription>
              {inviteStatus.status === 'loading' &&
                'Please wait while we process your invitation...'}
              {inviteStatus.status === 'success' &&
                `You've successfully joined ${inviteStatus.orgName}`}
              {inviteStatus.status === 'error' && 'There was an issue processing your invitation'}
              {inviteStatus.status === 'invalid' && 'This invitation link is not valid'}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Invitation Details */}
            {orgId && email && (
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>Email: {email}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <ExternalLink className="w-4 h-4" />
                  <span>Organization: {orgName}</span>
                </div>
              </div>
            )}

            {/* Status Message */}
            <Alert
              className={
                inviteStatus.status === 'error' || inviteStatus.status === 'invalid'
                  ? 'border-red-200'
                  : ''
              }
            >
              <AlertDescription>{inviteStatus.message}</AlertDescription>
            </Alert>

            {/* Action Buttons */}
            <div className="flex flex-col space-y-2">
              {inviteStatus.status === 'success' && (
                <Button
                  onClick={() => router.push('/dashboard')}
                  className="w-full bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700"
                >
                  Go to Dashboard
                </Button>
              )}

              {(inviteStatus.status === 'error' || inviteStatus.status === 'invalid') && (
                <div className="space-y-2">
                  <Button
                    onClick={() => window.location.reload()}
                    variant="outline"
                    className="w-full"
                  >
                    Try Again
                  </Button>
                  <Button onClick={() => router.push('/signin')} className="w-full">
                    Sign In
                  </Button>
                </div>
              )}
            </div>

            {/* Auto-redirect notice */}
            {inviteStatus.status === 'success' && (
              <p className="text-xs text-center text-gray-500">
                You'll be redirected to the dashboard in a few seconds...
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function AcceptInvitePage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
          <Loader2 className="w-8 h-8 text-white animate-spin" />
        </div>
      }
    >
      <AcceptInviteContent />
    </Suspense>
  );
}
