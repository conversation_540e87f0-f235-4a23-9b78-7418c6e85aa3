// app/auth/actions.ts
'use server';

import {
  signIn,
  signUp,
  confirmSignUp,
  resetPassword,
  confirmResetPassword,
  signOut as amplifySignOut,
  fetchAuthSession,
  getCurrentUser,
  AuthTokens,
} from '@aws-amplify/auth';
import { Amplify } from 'aws-amplify';
import { signInWithRedirect } from 'aws-amplify/auth';

import { redirect } from 'next/navigation';
import {
  createSession,
  setSessionCookie,
  clearSession,
  generateCSRFToken,
  verifyCSRFToken,
  getSession as getSecureSession,
  setCognitoTokenCookies,
  cleanupOrphanedSessions,
  verifySession,
} from '@/lib/session';
import { RedisTokenStorage } from '@/lib/redis-token-storage';
import { invalidateUserRoles } from '@/lib/external-roles';
import {
  AuthResult,
  TokenStorage,
  AuthConfig,
  CognitoErrorType,
  AuthStepType,
  AuthFormState,
  Permission,
  UserDetails,
} from '@/types/auth';
import { mapRolesToPermissions } from '@/lib/permissions';

/**
 * Redis-based token storage for server-side Amplify operations
 * Provides scalable token storage with automatic fallback to in-memory storage
 */
const serverTokenStorage = new RedisTokenStorage();

/**
 * Ensures Amplify is properly configured for server-side operations
 *
 * This function configures Amplify with Cognito settings and custom token storage
 * to prevent "Amplify has not been configured" errors in server actions.
 *
 * IMPORTANT: This clears any existing authentication state to prevent
 * UserAlreadyAuthenticatedException when multiple users sign in.
 */
const ensureAmplifyConfig = async () => {
  const config: AuthConfig = {
    Auth: {
      Cognito: {
        userPoolId: process.env.NEXT_PUBLIC_QBRAID_COGNITO_USER_POOL_ID!,
        userPoolClientId: process.env.NEXT_PUBLIC_QBRAID_COGNITO_CLIENTID!,
        identityPoolId: process.env.NEXT_PUBLIC_QBRAID_COGNITO_IDENTITY_POOL_ID!,
        signUpVerificationMethod: 'link' as const,
        loginWith: {
          email: true,
          username: false,
          phone: false,
        },
      },
    },
    Storage: serverTokenStorage,
  };

  try {
    console.log('🔧 [AUTH] Configuring Amplify for server-side with custom token storage...');

    // Clear any existing authentication state to prevent UserAlreadyAuthenticatedException
    // This is crucial for server-side operations where multiple users might sign in
    try {
      await amplifySignOut({ global: true });
      console.log('🔧 [AUTH] Cleared existing auth state to prevent conflicts');
    } catch (signOutError) {
      // Ignore sign out errors - user might not be signed in
      console.log('🔧 [AUTH] No existing auth state to clear (expected)');
    }

    // Clear the token storage to ensure fresh state
    try {
      await serverTokenStorage.clear();
      console.log('🔧 [AUTH] Cleared token storage for fresh authentication');
    } catch (storageError) {
      console.warn('⚠️ [AUTH] Failed to clear token storage:', storageError);
    }

    // Configure Amplify with fresh state
    Amplify.configure(config);
  } catch (error) {
    console.error('Failed to configure Amplify:', error);
    // Try configuring without SSR option as fallback
    try {
      Amplify.configure(config);
    } catch (fallbackError) {
      console.error('Fallback Amplify configuration failed:', fallbackError);
    }
  }
};

/**
 * Validates CSRF token for state-changing operations
 */
async function validateCSRFToken(formData: FormData): Promise<boolean> {
  const csrfToken = formData.get('csrfToken') as string;
  if (!csrfToken) {
    console.error('CSRF token missing from form submission');
    return false;
  }
  return await verifyCSRFToken(csrfToken);
}

/**
 * Authenticates a user with email and password
 *
 * This server action handles user sign-in with comprehensive error handling.
 * It creates a secure HTTP-only cookie session on successful authentication.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing email and password
 * @returns Promise<AuthResult> - Authentication result with success/error info
 */
export async function authenticateUser(
  prevState: AuthFormState | null,
  formData: FormData,
): Promise<AuthResult> {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const startTime = Date.now();

  console.log('🚀 [AUTH] Starting authentication process for user:', email);
  console.log('📊 [AUTH] Authentication request timestamp:', new Date().toISOString());

  try {
    // Ensure Amplify is configured before any auth operations
    console.log('🔧 [AUTH] Ensuring Amplify configuration...');
    await ensureAmplifyConfig();
    console.log('✅ [AUTH] Amplify configuration complete');

    // Validate CSRF token for security
    console.log('🛡️ [AUTH] Validating CSRF token...');
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      console.error('❌ [AUTH] CSRF token validation failed for user:', email);
      return { success: false, error: 'Invalid request. Please try again.' };
    }
    console.log('✅ [AUTH] CSRF token validation passed');

    // Basic input validation
    if (!email || !password) {
      console.error('❌ [AUTH] Missing required fields - email:', !!email, 'password:', !!password);
      return { success: false, error: 'Email and password are required' };
    }
    console.log('✅ [AUTH] Input validation passed for email:', email);

    console.log('🔐 [AUTH] Attempting sign in for:', email);
    const result = await signIn({ username: email, password });
    console.debug(result);
    console.log('✅ [AUTH] Sign in result:', {
      isSignedIn: result.isSignedIn,
      nextStep: result.nextStep,
    });

    if (result.isSignedIn) {
      console.log('🎫 [AUTH] Sign in successful, getting user details and session...');

      // Get user details and session tokens
      let userId = email; // fallback
      let tokens = null;

      try {
        // Get current user details
        const userDetails = await getCurrentUser();
        userId = userDetails.userId || userDetails.username || email;
        console.log('👤 [AUTH] User details:', {
          userId: userId,
          username: userDetails.username,
          email: email,
        });
        console.debug('--------------------------------');
        console.debug('USERDETAILS::::\n', userDetails);
        console.debug('--------------------------------');
        // Get auth session with tokens
        const authSession = await fetchAuthSession();
        tokens = authSession.tokens as AuthTokens;
        console.debug('--------------------------------');
        console.debug('Full AuthSession object:', JSON.stringify(authSession, null, 2));
        console.debug('AuthSession credentials:', authSession.credentials);
        console.debug('Authsession :::: Access Token', authSession.tokens?.accessToken);
        console.debug('Authsession :::: ID Token', authSession.tokens?.idToken);
        console.debug('--------------------------------');
        console.log('🎫 [AUTH] Auth session tokens:', {
          hasAccessToken: !!tokens?.accessToken,
          hasIdToken: !!tokens?.idToken,
          accessTokenLength: tokens?.accessToken?.toString().length || 0,
          idTokenLength: tokens?.idToken?.toString().length || 0,
        });
      } catch (sessionError) {
        console.warn('⚠️ [AUTH] Could not get user details or tokens:', sessionError);
        // Continue with email as userId
      }

      // Skip external role fetching during authentication
      console.log(
        '⏭️ [AUTH] Skipping external role fetching during authentication (will be fetched after login)',
      );
      const externalRoles: string[] = [];
      const permissions = mapRolesToPermissions(externalRoles);
      console.log(
        '🎭 [AUTH] Using empty roles for initial session (roles will be fetched post-authentication)',
      );

      // Create secure session with roles and permissions
      console.log('🎫 [AUTH] Creating secure session with user data...');
      const sessionCreateStart = Date.now();
      const sessionToken = await createSession({
        username: email,
        email: email,
        userId: userId,
        externalRoles,
        permissions,
      });
      const sessionCreateTime = Date.now() - sessionCreateStart;
      console.log('✅ [AUTH] Session created successfully in', sessionCreateTime, 'ms:', {
        sessionId: sessionToken.substring(0, 10) + '...',
        userId: userId,
        email: email,
        rolesCount: externalRoles.length,
        permissionsCount: permissions.length,
        createDuration: sessionCreateTime + 'ms',
      });

      // Store Cognito tokens with session ID for Redis mode
      if (tokens) {
        console.log('💾 [AUTH] Storing Cognito tokens in Redis...');
        const tokenStoreStart = Date.now();
        await setCognitoTokenCookies(
          {
            accessToken: tokens.accessToken?.toString(),
            idToken: tokens.idToken?.toString(),
          },
          sessionToken,
        ); // Pass session ID for Redis storage
        const tokenStoreTime = Date.now() - tokenStoreStart;

        console.log('✅ [AUTH] Cognito tokens stored successfully in', tokenStoreTime, 'ms:', {
          hasAccessToken: !!tokens.accessToken,
          hasIdToken: !!tokens.idToken,
          accessTokenLength: tokens.accessToken?.toString().length || 0,
          idTokenLength: tokens.idToken?.toString().length || 0,
          sessionId: sessionToken.substring(0, 10) + '...',
          storeDuration: tokenStoreTime + 'ms',
        });
      } else {
        console.warn('⚠️ [AUTH] No tokens available to store in Redis');
      }

      // Set secure session cookie
      console.log('🍪 [AUTH] Setting secure session cookie...');
      const cookieSetStart = Date.now();
      await setSessionCookie(sessionToken);
      const cookieSetTime = Date.now() - cookieSetStart;
      console.log('✅ [AUTH] Session cookie set successfully in', cookieSetTime, 'ms');

      // Clean up any expired sessions for this user (optional maintenance)
      console.log('🧹 [AUTH] Starting session cleanup for user...');
      const cleanupStart = Date.now();
      try {
        await cleanupOrphanedSessions(email, sessionToken);
        const cleanupTime = Date.now() - cleanupStart;
        console.log('✅ [AUTH] Session cleanup completed successfully in', cleanupTime, 'ms');
      } catch (cleanupError) {
        const cleanupTime = Date.now() - cleanupStart;
        console.warn(
          '⚠️ [AUTH] Session cleanup failed (non-critical) after',
          cleanupTime,
          'ms:',
          cleanupError,
        );
      }

      const totalTime = Date.now() - startTime;
      console.log('🎉 [AUTH] Authentication completed successfully!');
      console.log('📈 [AUTH] Total authentication time:', totalTime, 'ms');
      console.log('📊 [AUTH] Authentication summary:', {
        email: email,
        userId: userId,
        rolesCount: externalRoles.length,
        permissionsCount: permissions.length,
        totalDuration: totalTime + 'ms',
        timestamp: new Date().toISOString(),
      });

      return { success: true, redirectTo: '/' };
    }

    // Handle multi-step authentication flows (e.g., unverified users)
    // Use generic messages to prevent information disclosure
    if (result.nextStep) {
      switch (result.nextStep.signInStep as AuthStepType) {
        case 'CONFIRM_SIGN_UP':
          return {
            success: false,
            requiresVerification: true,
            error: 'Invalid email or password',
          };
        case 'RESET_PASSWORD':
          return {
            success: false,
            error: 'Invalid email or password',
          };
        default:
          return {
            success: false,
            error: 'Authentication failed. Please try again.',
          };
      }
    }

    return { success: false, error: 'Authentication failed' };
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [AUTH] Authentication failed after', totalTime, 'ms');
    console.error('🚨 [AUTH] Error details:', {
      email: email,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      stackTrace: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      totalDuration: totalTime + 'ms',
    });

    // Map Cognito error types to generic user-friendly messages
    // Use generic messages to prevent information disclosure about registered emails
    const errorName = error instanceof Error ? (error.name as CognitoErrorType) : undefined;
    console.log('🔍 [AUTH] Processing error type:', errorName);
    switch (errorName) {
      case 'UserNotConfirmedException':
        return {
          success: false,
          requiresVerification: true,
          error: 'Invalid email or password',
        };
      case 'NotAuthorizedException':
        return { success: false, error: 'Invalid email or password' };
      case 'UserNotFoundException':
        return { success: false, error: 'Invalid email or password' };
      case 'PasswordResetRequiredException':
        return { success: false, error: 'Invalid email or password' };
      case 'TooManyRequestsException':
        return { success: false, error: 'Too many attempts. Please try again later.' };
      case 'UnexpectedSignInInterruptionException':
        console.log(
          '🔧 [AUTH] Handling UnexpectedSignInInterruptionException - attempting recovery...',
        );

        // Special case: Sometimes Cognito throws this but user is actually signed in
        // Try to get user details and create session anyway
        let recoveredUserId = email; // fallback

        try {
          const userDetails = await getCurrentUser();
          recoveredUserId = userDetails.userId || userDetails.username || email;
          console.log('✅ [AUTH] Successfully recovered user details:', {
            userId: recoveredUserId,
            username: userDetails.username,
          });
        } catch {
          console.warn('⚠️ [AUTH] Could not recover user details, using email as userId');
        }

        // Skip external role fetching for recovery case
        console.log(
          '⏭️ [AUTH] Skipping external role fetching for recovered user (will be fetched after login)',
        );
        const externalRoles: string[] = [];
        const permissions: Permission[] = mapRolesToPermissions(externalRoles);
        console.log(
          '🎭 [AUTH] Using empty roles for recovered session (roles will be fetched post-authentication)',
        );

        // Create secure JWT session with recovered userId and roles
        const sessionToken = await createSession({
          username: email,
          email: email,
          userId: recoveredUserId,
          externalRoles,
          permissions,
        });
        await setSessionCookie(sessionToken);

        // Clean up any expired sessions for this user (optional maintenance)
        try {
          await cleanupOrphanedSessions(email, sessionToken);
        } catch (cleanupError) {
          console.warn('⚠️ [AUTH] Session cleanup failed (non-critical):', cleanupError);
        }

        console.log('✅ [AUTH] Successfully recovered from UnexpectedSignInInterruptionException');
        return { success: true, redirectTo: '/' };
      default:
        return {
          success: false,
          error: 'Authentication failed. Please try again.',
        };
    }
  }
}

/**
 * Registers a new user account
 *
 * Creates a new user in Cognito with email verification required.
 * Validates input data and handles registration errors appropriately.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing name, email, password, confirmPassword
 * @returns Promise<AuthResult> - Registration result with next steps
 */
export async function registerUser(
  prevState: AuthFormState | null,
  formData: FormData,
): Promise<AuthResult> {
  const name = formData.get('name') as string;
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;
  const startTime = Date.now();

  console.log('📝 [REGISTER] Starting user registration process for:', email);
  console.log('📊 [REGISTER] Registration request timestamp:', new Date().toISOString());

  try {
    console.log('🔧 [REGISTER] Ensuring Amplify configuration...');
    await ensureAmplifyConfig();
    console.log('✅ [REGISTER] Amplify configuration complete');

    // Validate CSRF token for security
    console.log('🛡️ [REGISTER] Validating CSRF token...');
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      console.error('❌ [REGISTER] CSRF token validation failed for user:', email);
      return { success: false, error: 'Invalid request. Please try again.' };
    }
    console.log('✅ [REGISTER] CSRF token validation passed');

    // Validate required fields
    console.log('🔍 [REGISTER] Validating required fields...');
    if (!name || !email || !password) {
      console.error(
        '❌ [REGISTER] Missing required fields - name:',
        !!name,
        'email:',
        !!email,
        'password:',
        !!password,
      );
      return { success: false, error: 'Name, email and password are required' };
    }

    // Validate password confirmation
    if (password !== confirmPassword) {
      console.error('❌ [REGISTER] Password confirmation mismatch for user:', email);
      return { success: false, error: 'Passwords do not match' };
    }

    // Basic password strength validation
    if (password.length < 8) {
      console.error('❌ [REGISTER] Password too short for user:', email);
      return { success: false, error: 'Password must be at least 8 characters long' };
    }
    console.log('✅ [REGISTER] Input validation passed for user:', email);

    console.log('📤 [REGISTER] Attempting Cognito sign up for:', email);
    const signUpStart = Date.now();
    const result = await signUp({
      username: email, // Use email as username
      password,
      options: {
        userAttributes: {
          email,
          name,
          preferred_username: email,
        },
      },
    });
    const signUpTime = Date.now() - signUpStart;

    console.log('📥 [REGISTER] Cognito sign up response received in', signUpTime, 'ms:', {
      nextStep: result.nextStep?.signUpStep,
      email: email,
      signUpDuration: signUpTime + 'ms',
    });

    // Most sign-ups require email verification
    if (result.nextStep?.signUpStep === 'CONFIRM_SIGN_UP') {
      const totalTime = Date.now() - startTime;
      console.log('✅ [REGISTER] Registration successful, verification required');
      console.log('📊 [REGISTER] Registration summary:', {
        email: email,
        name: name,
        nextStep: 'CONFIRM_SIGN_UP',
        totalDuration: totalTime + 'ms',
        timestamp: new Date().toISOString(),
      });
      return {
        success: true,
        nextStep: 'verify',
        email: email,
        error: 'Please check your email and click the verification link',
      };
    }

    const totalTime = Date.now() - startTime;
    console.log('🎉 [REGISTER] Registration completed successfully in', totalTime, 'ms');
    return { success: true };
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [REGISTER] Registration failed after', totalTime, 'ms');
    console.error('🚨 [REGISTER] Error details:', {
      email: email,
      name: name,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
      totalDuration: totalTime + 'ms',
    });

    // Handle common registration errors
    const errorName = error instanceof Error ? error.name : undefined;
    switch (errorName) {
      case 'UsernameExistsException':
        return { success: false, error: 'An account with this email already exists' };
      case 'InvalidPasswordException':
        return { success: false, error: 'Password does not meet requirements' };
      case 'InvalidParameterException':
        return { success: false, error: 'Invalid email format' };
      default:
        return {
          success: false,
          error: 'Registration failed. Please try again.',
        };
    }
  }
}

/**
 * Verifies user email with confirmation code
 *
 * @deprecated This function is legacy for code-based verification.
 * New implementation uses link-based verification via /api/auth/verify-link
 *
 * Completes the user registration process by confirming the email
 * with the verification code sent by Cognito.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing email and verification code
 * @returns Promise<AuthResult> - Verification result
 */
export async function verifyEmail(
  prevState: AuthFormState | null,
  formData: FormData,
): Promise<AuthResult> {
  try {
    await ensureAmplifyConfig();

    // Validate CSRF token for security
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;
    const code = formData.get('code') as string;

    // Validate required inputs
    if (!email || !code) {
      return { success: false, error: 'Email and verification code are required' };
    }

    console.log('Attempting email verification for:', email, 'with code:', code);
    await confirmSignUp({
      username: email,
      confirmationCode: code,
    });

    console.log('Email verification successful for:', email);

    return { success: true };
  } catch (error: unknown) {
    console.error('Verification error:', error);

    // Handle verification-specific errors
    const errorName = error instanceof Error ? error.name : undefined;
    switch (errorName) {
      case 'CodeMismatchException':
        return { success: false, error: 'Invalid verification code' };
      case 'ExpiredCodeException':
        return { success: false, error: 'Verification code has expired' };
      case 'NotAuthorizedException':
        return { success: false, error: 'User is already verified' };
      case 'AliasExistsException':
        // User is already confirmed, treat as success
        return { success: true };
      default:
        return {
          success: false,
          error: 'Verification failed. Please try again.',
        };
    }
  }
}

/**
 * Initiates password reset process
 *
 * Sends a password reset code to the user's email address.
 * Used for "forgot password" functionality.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing email address
 * @returns Promise<AuthResult> - Reset initiation result
 */
export async function initiatePasswordReset(
  prevState: AuthFormState | null,
  formData: FormData,
): Promise<AuthResult> {
  try {
    await ensureAmplifyConfig();

    // Validate CSRF token for security
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;

    if (!email) {
      return { success: false, error: 'Email is required' };
    }

    await resetPassword({ username: email });
    return {
      success: true,
      nextStep: 'reset',
      error: 'Password reset code sent to your email',
    };
  } catch (error: unknown) {
    console.error('Password reset error:', error);

    const errorName = error instanceof Error ? error.name : undefined;
    switch (errorName) {
      case 'UserNotFoundException':
        return { success: false, error: 'No account found with this email' };
      case 'NotAuthorizedException':
        return { success: false, error: 'User account is disabled' };
      default:
        return {
          success: false,
          error: 'Failed to send reset code. Please try again.',
        };
    }
  }
}

/**
 * Completes password reset with new password
 *
 * Uses the reset code sent via email to set a new password.
 * Validates password confirmation and strength requirements.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing email, code, and new passwords
 * @returns Promise<AuthResult> - Reset completion result
 */
export async function completePasswordReset(
  prevState: AuthFormState | null,
  formData: FormData,
): Promise<AuthResult> {
  try {
    await ensureAmplifyConfig();

    // Validate CSRF token for security
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;
    const code = formData.get('code') as string;
    const newPassword = formData.get('newPassword') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    // Validate all required fields
    if (!email || !code || !newPassword) {
      return { success: false, error: 'All fields are required' };
    }

    // Validate password confirmation
    if (newPassword !== confirmPassword) {
      return { success: false, error: 'Passwords do not match' };
    }

    // Basic password strength validation
    if (newPassword.length < 8) {
      return { success: false, error: 'Password must be at least 8 characters long' };
    }

    await confirmResetPassword({
      username: email,
      confirmationCode: code,
      newPassword,
    });

    return { success: true };
  } catch (error: unknown) {
    console.error('Password reset completion error:', error);

    const errorName = error instanceof Error ? error.name : undefined;
    switch (errorName) {
      case 'CodeMismatchException':
        return { success: false, error: 'Invalid reset code' };
      case 'ExpiredCodeException':
        return { success: false, error: 'Reset code has expired' };
      case 'InvalidPasswordException':
        return { success: false, error: 'Password does not meet requirements' };
      default:
        return {
          success: false,
          error: 'Password reset failed. Please try again.',
        };
    }
  }
}

/**
 * Logs out the current user
 *
 * Performs Amplify sign out and cleans up the session cookie.
 * Redirects to sign-in page after successful logout.
 *
 * Note: This function always redirects, so it doesn't return a value.
 */
export async function logout() {
  const startTime = Date.now();
  let currentSessionId: string | null = null;
  let userEmail: string | null = null;

  console.log('🚪 [LOGOUT] Starting logout process...');
  console.log('📊 [LOGOUT] Logout request timestamp:', new Date().toISOString());

  try {
    console.log('🔧 [LOGOUT] Ensuring Amplify configuration...');
    await ensureAmplifyConfig();

    // Get current session ID before clearing for proper Redis cleanup
    console.log('🔍 [LOGOUT] Getting current session details...');
    const currentSession = await getSecureSession();
    currentSessionId = currentSession?.jti || null;
    userEmail = currentSession?.email || null;

    console.log('📋 [LOGOUT] Session details:', {
      sessionId: currentSessionId?.substring(0, 10) + '...',
      userEmail: userEmail,
      hasSession: !!currentSession,
    });

    // Sign out from Cognito
    console.log('📤 [LOGOUT] Signing out from Cognito...');
    const cognitoSignOutStart = Date.now();
    await amplifySignOut();
    const cognitoSignOutTime = Date.now() - cognitoSignOutStart;
    console.log('✅ [LOGOUT] Cognito sign out completed in', cognitoSignOutTime, 'ms');
  } catch (error) {
    const errorTime = Date.now() - startTime;
    console.error('❌ [LOGOUT] Logout error after', errorTime, 'ms:', error);
    console.error('🚨 [LOGOUT] Error details:', {
      sessionId: currentSessionId?.substring(0, 10) + '...',
      userEmail: userEmail,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    });
    // Continue with cleanup even if Amplify logout fails
  } finally {
    // Invalidate user roles cache
    if (userEmail) {
      try {
        console.log('🗑️ [LOGOUT] Invalidating user roles cache for:', userEmail);
        await invalidateUserRoles(userEmail);
        console.log('✅ [LOGOUT] User roles cache invalidated successfully');
      } catch (cacheError) {
        console.error('❌ [LOGOUT] Failed to invalidate user roles cache:', cacheError);
        // Continue with logout even if cache invalidation fails
      }
    }

    // Clear all session and security cookies with session ID for proper Redis cleanup
    console.log('🧹 [LOGOUT] Clearing session and cookies...');
    const cleanupStart = Date.now();
    await clearSession(currentSessionId || undefined);
    const cleanupTime = Date.now() - cleanupStart;
    const totalTime = Date.now() - startTime;

    console.log('✅ [LOGOUT] Session cleanup completed in', cleanupTime, 'ms');
    console.log('🎉 [LOGOUT] Logout process completed successfully!');
    console.log('📊 [LOGOUT] Logout summary:', {
      sessionId: currentSessionId?.substring(0, 10) + '...',
      userEmail: userEmail,
      totalDuration: totalTime + 'ms',
      timestamp: new Date().toISOString(),
    });

    redirect('/signin');
  }
}

/**
 * Retrieves the current user session using secure JWT tokens
 *
 * Verifies the JWT session token and returns validated session data.
 * Used for server-side authentication checks.
 *
 * @returns Promise<object | null> - Session data or null if not authenticated
 */
export async function getSession() {
  return await getSecureSession();
}

/**
 * Validates current session and forces logout if invalid
 *
 * This function is useful for components that need to ensure the session is valid
 * and automatically log out the user if Redis data is missing
 */
export async function validateSessionOrLogout() {
  const session = await getSecureSession();

  if (!session) {
    console.log('🚪 [AUTH] Invalid session detected, forcing logout...');
    // Don't await this to avoid blocking
    logout().catch((error) => {
      console.error('❌ [AUTH] Failed to force logout:', error);
    });
    return null;
  }

  return session;
}

/**
 * Generates a CSRF token for form protection
 *
 * Creates a new CSRF token and returns it for inclusion in forms.
 * Used to protect against Cross-Site Request Forgery attacks.
 *
 * @returns Promise<string> - CSRF token
 */
export async function getCSRFToken(): Promise<string> {
  return await generateCSRFToken();
}

/**
 * Initiates Google OAuth sign-in flow
 *
 * Redirects the user to Google for authentication via Cognito hosted UI.
 * After successful authentication, user will be redirected back to the app.
 */
export async function signInWithGoogle(): Promise<void> {
  try {
    await ensureAmplifyConfig();

    console.log('🔐 [AUTH] Initiating Google OAuth sign-in...');

    // Redirect to Google OAuth via Cognito hosted UI
    await signInWithRedirect({
      provider: 'Google',
      customState: JSON.stringify({
        returnUrl: '/',
      }),
    });
  } catch (error: unknown) {
    console.error('❌ [AUTH] Google sign-in error:', error);
    throw new Error('Failed to initialize Google sign-in');
  }
}

/**
 * Handles OAuth callback and session creation
 *
 * This should be called on the page where users land after OAuth redirect.
 * It creates a secure session cookie from the OAuth tokens.
 */
export async function handleOAuthCallback(): Promise<AuthResult> {
  try {
    await ensureAmplifyConfig();

    console.log('🔄 [AUTH] Processing OAuth callback...');

    // Get current user details after OAuth
    const userDetails = await getCurrentUser();
    const authSession = await fetchAuthSession();

    if (!userDetails || !authSession.tokens) {
      return {
        success: false,
        error: 'OAuth authentication failed',
      };
    }

    const tokens = authSession.tokens as AuthTokens;
    const userId = userDetails.userId || userDetails.username;
    const email = userDetails.signInDetails?.loginId || 'oauth-user';

    console.log('✅ [AUTH] OAuth user authenticated:', {
      userId,
      email,
      hasTokens: !!tokens,
    });

    // Store Cognito tokens in secure cookies
    if (tokens) {
      await setCognitoTokenCookies({
        accessToken: tokens.accessToken?.toString(),
        idToken: tokens.idToken?.toString(),
      });
    }

    // Create secure JWT session
    const sessionToken = await createSession({
      username: email,
      email: email,
      userId: userId,
    });

    await setSessionCookie(sessionToken);

    // Clean up any expired sessions for this user (optional maintenance)
    try {
      await cleanupOrphanedSessions(email, sessionToken);
    } catch (cleanupError) {
      console.warn('⚠️ [AUTH] Session cleanup failed (non-critical):', cleanupError);
    }

    console.log('✅ [AUTH] OAuth session created successfully');

    return {
      success: true,
      redirectTo: '/',
    };
  } catch (error: unknown) {
    console.error('❌ [AUTH] OAuth callback error:', error);
    return {
      success: false,
      error: 'Failed to complete OAuth authentication',
    };
  }
}
/**
 * Creates session after successful OAuth authentication
 *
 * This server action should be called from the client after OAuth Hub events
 * confirm successful authentication. It handles session creation with user data.
 */
export async function createOAuthSession(userData: {
  userId: string;
  email: string;
  username?: string;
  tokens?: {
    accessToken: string;
    idToken: string;
  };
}): Promise<AuthResult> {
  const startTime = Date.now();

  try {
    await ensureAmplifyConfig();

    console.log('🎫 [OAUTH-SESSION] Creating session for OAuth user:', userData);

    const { userId, email, username, tokens } = userData;

    // Create session first to get sessionId for Redis storage
    console.log('🎫 [OAUTH-SESSION] Creating secure session...');
    const externalRoles: string[] = [];
    const permissions = mapRolesToPermissions(externalRoles);

    const sessionToken = await createSession({
      username: username || email,
      email: email,
      userId: userId,
      externalRoles,
      permissions,
    });

    // Extract sessionId from the created session token
    const sessionData = await verifySession(sessionToken);
    const sessionId = sessionData?.jti;

    // Store OAuth tokens in Redis using sessionId
    if (tokens && sessionId) {
      console.log('💾 [OAUTH-SESSION] Storing OAuth tokens in Redis...');
      await setCognitoTokenCookies(
        {
          accessToken: tokens.accessToken,
          idToken: tokens.idToken,
        },
        sessionId,
      );
      console.log('✅ [OAUTH-SESSION] OAuth tokens stored in Redis successfully');
    } else if (tokens) {
      console.log('💾 [OAUTH-SESSION] Storing OAuth tokens in cookies (no sessionId)...');
      await setCognitoTokenCookies({
        accessToken: tokens.accessToken,
        idToken: tokens.idToken,
      });
      console.log('✅ [OAUTH-SESSION] OAuth tokens stored in cookies');
    } else {
      console.warn('⚠️ [OAUTH-SESSION] No OAuth tokens provided');
    }

    await setSessionCookie(sessionToken);
    console.log('✅ [OAUTH-SESSION] OAuth session created successfully');

    // Clean up any expired sessions for this user (optional maintenance)
    try {
      await cleanupOrphanedSessions(email, sessionToken);
    } catch (cleanupError) {
      console.warn('⚠️ [OAUTH-SESSION] Session cleanup failed (non-critical):', cleanupError);
    }

    const totalTime = Date.now() - startTime;
    console.log('🎉 [OAUTH-SESSION] OAuth session creation completed successfully!');
    console.log('📊 [OAUTH-SESSION] Session summary:', {
      userId,
      email,
      username,
      rolesCount: externalRoles.length,
      permissionsCount: permissions.length,
      totalDuration: totalTime + 'ms',
      timestamp: new Date().toISOString(),
    });

    return {
      success: true,
      redirectTo: '/',
    };
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [OAUTH-SESSION] Session creation error after', totalTime, 'ms:', error);
    console.error('🚨 [OAUTH-SESSION] Error details:', {
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      stackTrace: error instanceof Error ? error.stack : undefined,
      userData,
      totalDuration: totalTime + 'ms',
      timestamp: new Date().toISOString(),
    });

    return {
      success: false,
      error: 'Failed to create authentication session',
    };
  }
}
