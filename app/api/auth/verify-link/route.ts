import { NextRequest, NextResponse } from 'next/server';
import { confirmSignUp } from 'aws-amplify/auth';
import { configureAmplify } from '@/lib/amplify-config';

export async function POST(request: NextRequest) {
  try {
    // Ensure Amplify is configured
    configureAmplify();

    const { username, confirmationCode } = await request.json();

    if (!username || !confirmationCode) {
      return NextResponse.json(
        { success: false, error: 'Username and confirmation code are required' },
        { status: 400 },
      );
    }

    console.log('🔗 [VERIFY-LINK] Processing email verification for:', username);

    // Confirm the sign up using the verification link parameters
    await confirmSignUp({
      username: username,
      confirmationCode: confirmationCode,
    });

    console.log('✅ [VERIFY-LINK] Email verification successful for:', username);

    return NextResponse.json({
      success: true,
      message: 'Email verified successfully',
    });
  } catch (error: unknown) {
    console.error('❌ [VERIFY-LINK] Verification error:', error);

    // Handle verification-specific errors
    const errorName = error instanceof Error ? error.name : undefined;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    let userFriendlyError = 'Verification failed. Please try again.';

    switch (errorName) {
      case 'CodeMismatchException':
        userFriendlyError = 'Invalid verification link. Please request a new one.';
        break;
      case 'ExpiredCodeException':
        userFriendlyError = 'Verification link has expired. Please request a new one.';
        break;
      case 'NotAuthorizedException':
        userFriendlyError = 'User is already verified.';
        break;
      case 'AliasExistsException':
        // User is already confirmed, treat as success
        return NextResponse.json({
          success: true,
          message: 'Email already verified',
        });
      default:
        console.error('🚨 [VERIFY-LINK] Unexpected error:', {
          errorName,
          errorMessage,
          username,
        });
    }

    return NextResponse.json({ success: false, error: userFriendlyError }, { status: 400 });
  }
}
