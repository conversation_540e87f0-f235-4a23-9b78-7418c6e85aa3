import { NextRequest, NextResponse } from 'next/server';
import { resendSignUpCode } from 'aws-amplify/auth';
import { configureAmplify } from '@/lib/amplify-config';

export async function POST(request: NextRequest) {
  try {
    // Ensure Amplify is configured
    configureAmplify();

    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ success: false, error: 'Email is required' }, { status: 400 });
    }

    console.log('📤 [RESEND-VERIFICATION] Resending verification link to:', email);

    // Resend the verification link
    await resendSignUpCode({
      username: email,
    });

    console.log('✅ [RESEND-VERIFICATION] Verification link sent to:', email);

    return NextResponse.json({
      success: true,
      message: 'Verification link sent successfully',
    });
  } catch (error: unknown) {
    console.error('❌ [RESEND-VERIFICATION] Error:', error);

    // Handle resend-specific errors
    const errorName = error instanceof Error ? error.name : undefined;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    let userFriendlyError = 'Failed to send verification link. Please try again.';

    switch (errorName) {
      case 'UserNotFoundException':
        userFriendlyError = 'No account found with this email address.';
        break;
      case 'NotAuthorizedException':
        userFriendlyError = 'User account is already verified or disabled.';
        break;
      case 'LimitExceededException':
        userFriendlyError = 'Too many attempts. Please wait before requesting another link.';
        break;
      case 'InvalidParameterException':
        userFriendlyError = 'Invalid email address format.';
        break;
      default:
        console.error('🚨 [RESEND-VERIFICATION] Unexpected error:', {
          errorName,
          errorMessage,
          email,
        });
    }

    return NextResponse.json({ success: false, error: userFriendlyError }, { status: 400 });
  }
}
