import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';
import { z } from 'zod';

// Validation schema for invite request
const inviteUserSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  role: z.string().min(1, { message: 'Role is required' }),
  orgId: z.string().min(1, { message: 'Organization ID is required' }),
  orgName: z.string().min(1, { message: 'Organization name is required' }),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log('🚀 [INVITE-USER-API] Starting user invitation request');
  console.log('📊 [INVITE-USER-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = inviteUserSchema.parse(body);

    console.log('✅ [INVITE-USER-API] Validation successful:', {
      requestId,
      email: validatedData.email,
      role: validatedData.role,
      orgId: validatedData.orgId,
      orgName: validatedData.orgName,
    });

    // Get session for auth context
    const session = await getSession();
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', message: 'Authentication required', success: false },
        { status: 401 },
      );
    }

    // Business logic validation
    if (session.email === validatedData.email) {
      return NextResponse.json(
        {
          error: 'Cannot invite yourself to the organization',
          message: 'Cannot invite yourself to the organization',
          success: false,
          code: 'SELF_INVITATION_FORBIDDEN',
        },
        { status: 400 },
      );
    }

    // Call external API with the correct format
    console.log('📤 [INVITE-USER-API] Making external API call...');
    const apiRes = await externalClient.post('/orgs/users/add', {
      name: validatedData.orgName, // External API expects 'name' not 'orgId'
      orgId: validatedData.orgId, // Include orgId for future-proofing
      email: validatedData.email,
      role: validatedData.role,
      referer: process.env.NEXT_PUBLIC_REFERRER,
    });

    console.log('📥 [INVITE-USER-API] External API response received:', {
      status: apiRes.status,
      data: apiRes.data,
    });

    // Check if the response indicates success based on the message
    const isSuccess =
      apiRes.data?.message === 'User invited' ||
      apiRes.data?.message?.includes('successfully') ||
      (!apiRes.data?.error && apiRes.data?.message);

    if (!isSuccess) {
      // Handle specific error messages from the external API
      const errorMessage = apiRes.data?.message || 'Failed to invite user';

      if (errorMessage.includes('already in organization')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'USER_EXISTS',
          },
          { status: 409 },
        );
      }

      if (errorMessage.includes('do not have permission')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'INSUFFICIENT_PERMISSIONS',
          },
          { status: 403 },
        );
      }

      if (errorMessage.includes('Organization not found')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'ORG_NOT_FOUND',
          },
          { status: 404 },
        );
      }
    }

    // Build successful response
    const response = {
      success: true,
      message: apiRes.data?.message || `User invited`,
      currentUserRole: validatedData.role,
      user: {
        email: validatedData.email,
        role: validatedData.role,
        status: 'Invited',
        invitedBy: session.email,
        invitedAt: new Date().toISOString(),
        orgId: validatedData.orgId,
        orgName: validatedData.orgName,
      },
      requestId,
      timestamp: new Date().toISOString(),
    };

    const totalTime = Date.now() - startTime;
    console.log('🎉 [INVITE-USER-API] Request completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      invitedEmail: validatedData.email,
      invitedRole: validatedData.role,
      orgName: validatedData.orgName,
    });

    // Add React Query invalidation headers
    const nextResponse = NextResponse.json(response);
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['orgUsers', validatedData.orgId], // Invalidate org users list
          ['userOrgRole', validatedData.email, validatedData.orgId], // Invalidate specific user role
          ['permissions'], // Invalidate permissions cache
        ],
        message: 'User invited - refresh org users and permissions',
      }),
    );

    return nextResponse;
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [INVITE-USER-API] Request failed after', totalTime, 'ms', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          message: error.errors[0]?.message || 'Validation failed',
          details: error.errors,
          success: false,
        },
        { status: 400 },
      );
    }

    // Handle external API errors
    if (error.response?.data?.message) {
      const errorMessage = error.response.data.message;

      if (errorMessage.includes('already in organization')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'USER_EXISTS',
          },
          { status: 409 },
        );
      }

      if (errorMessage.includes('do not have permission')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'INSUFFICIENT_PERMISSIONS',
          },
          { status: 403 },
        );
      }

      if (errorMessage.includes('Organization not found')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'ORG_NOT_FOUND',
          },
          { status: 404 },
        );
      }
    }

    return NextResponse.json(
      {
        error: error.message || 'Failed to invite user',
        message: error.message || 'Failed to invite user',
        success: false,
      },
      { status: error.status || 500 },
    );
  }
}
