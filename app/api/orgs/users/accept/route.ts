import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log('🚀 [ACCEPT-INVITATION-API] Starting accept invitation request');
  console.log('📊 [ACCEPT-INVITATION-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Get session for authentication
    const session = await getSession();
    if (!session?.email) {
      console.error('❌ [ACCEPT-INVITATION-API] Authentication failed:', { requestId });
      return NextResponse.json({ error: 'Authentication required', requestId }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { orgId, email } = body;

    console.log('📋 [ACCEPT-INVITATION-API] Request data:', {
      requestId,
      orgId,
      email,
      sessionEmail: session.email,
    });

    // Validate required fields
    if (!orgId || !email) {
      console.error('❌ [ACCEPT-INVITATION-API] Missing required fields:', {
        requestId,
        orgId: !!orgId,
        email: !!email,
      });
      return NextResponse.json(
        { error: 'orgId and email are required', requestId },
        { status: 400 },
      );
    }

    // Ensure user can only accept their own invitations
    if (email !== session.email) {
      console.error('❌ [ACCEPT-INVITATION-API] Email mismatch:', {
        requestId,
        providedEmail: email,
        sessionEmail: session.email,
      });
      return NextResponse.json(
        { error: 'You can only accept your own invitations', requestId },
        { status: 403 },
      );
    }

    // Call external API
    console.log('📤 [ACCEPT-INVITATION-API] Calling external API...');
    const externalApiStart = Date.now();
    const apiRes = await externalClient.post('/orgs/users/accept', { orgId, email });
    const externalApiTime = Date.now() - externalApiStart;

    console.log('📥 [ACCEPT-INVITATION-API] External API response:', {
      requestId,
      responseTime: externalApiTime + 'ms',
      status: apiRes.status,
      message: apiRes.data?.message || 'No message',
    });

    const totalTime = Date.now() - startTime;
    console.log('🎉 [ACCEPT-INVITATION-API] Request completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      orgId,
      email,
    });

    return NextResponse.json({
      success: true,
      message: apiRes.data?.message || 'Invitation accepted successfully',
      requestId,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [ACCEPT-INVITATION-API] Request failed after', totalTime, 'ms');
    console.error('🚨 [ACCEPT-INVITATION-API] Error details:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStatus: error.response?.status || 'unknown',
      totalDuration: totalTime + 'ms',
    });

    // Return the error message from the external API if available
    const errorMessage =
      error.response?.data?.message || error.message || 'Failed to accept invitation';
    const statusCode = error.response?.status || 500;

    return NextResponse.json(
      {
        error: errorMessage,
        message: errorMessage,
        requestId,
        timestamp: new Date().toISOString(),
        success: false,
      },
      { status: statusCode },
    );
  }
}
