import { NextRequest, NextResponse } from 'next/server';
import { getSession, getCognitoTokenCookies, getCurrentSessionId } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';

/**
 * API Gateway for Organization Users
 * GET /api/orgs/users/:orgId/:page/:limit - Get users in organization
 * This endpoint is used by both lib/external-roles.ts and the team page
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ params: string[] }> },
) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  try {
    const resolvedParams = await params;
    const [orgId, page = '0', limit = '10'] = resolvedParams.params;
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    console.log('🚀 [ORGS-USERS-API] Starting organization users fetch:', {
      requestId,
      orgId,
      page: pageNum,
      limit: limitNum,
      timestamp: new Date().toISOString(),
    });

    // Validate required parameters
    if (!orgId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    if (isNaN(pageNum) || pageNum < 0) {
      return NextResponse.json(
        { error: 'Page must be a non-negative integer', requestId },
        { status: 400 },
      );
    }

    if (isNaN(limitNum) || limitNum <= 0 || limitNum > 100) {
      return NextResponse.json(
        { error: 'Limit must be between 1 and 100', requestId },
        { status: 400 },
      );
    }

    // Get session for authentication
    const session = await getSession();
    if (!session) {
      console.warn('⚠️ [ORGS-USERS-API] No valid session found');
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get tokens for external API call
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);

    if (!tokens.idToken) {
      console.warn('⚠️ [ORGS-USERS-API] No ID token available');
      return NextResponse.json({ error: 'Authentication token unavailable' }, { status: 401 });
    }

    console.log(`📤 [ORGS-USERS-API] Making external API call for org ${orgId}...`);
    const externalApiStart = Date.now();

    const apiResponse = await externalClient.get(`/orgs/users/${orgId}/${page}/${limit}`);
    const externalApiTime = Date.now() - externalApiStart;

    console.log('📥 [ORGS-USERS-API] External API response received:', {
      requestId,
      orgId,
      responseSize: JSON.stringify(apiResponse.data).length,
      orgUsersCount: apiResponse.data?.orgUsers?.length || 0,
      apiCallDuration: externalApiTime + 'ms',
    });

    // Extract data from external API response
    const { orgUsers, pagination, message, currentUserRole } = apiResponse.data;

    // Transform orgUsers to match frontend expectations
    const transformedUsers = (orgUsers || []).map((user: any) => ({
      // Generate name from email if not provided
      name:
        user.name ||
        user.email
          .split('@')[0]
          .replace(/[._-]/g, ' ')
          .replace(/\b\w/g, (l: string) => l.toUpperCase()),
      email: user.email,
      firstName: user.firstName || user.email.split('@')[0],
      lastName: user.lastName || '',
      avatar: user.avatar || '/placeholder-user.jpg',
      role: user.role,
      status: user.accepted ? 'Active' : user.invited ? 'Invited' : 'Deactivated',
      userCredits: user.qBraidCredits || user.userCredits || 0,
      lastActive: user.lastLoggedIn || user.lastActive || new Date().toISOString(),
      // Preserve original fields
      _id: user._id,
      organization: user.organization,
      accepted: user.accepted,
      invited: user.invited,
    }));

    // Build response structure
    const response = {
      success: true,
      users: transformedUsers,
      orgUsers: transformedUsers, // Backward compatibility
      totalUsers: pagination?.totalUsers || pagination?.total || transformedUsers.length,
      currentPage: pageNum,
      totalPages:
        pagination?.totalPages ||
        Math.ceil(
          (pagination?.totalUsers || pagination?.total || transformedUsers.length) / limitNum,
        ),
      pageSize: limitNum,
      hasNextPage:
        pageNum <
        (pagination?.totalPages ||
          Math.ceil(
            (pagination?.totalUsers || pagination?.total || transformedUsers.length) / limitNum,
          )) -
          1,
      hasPreviousPage: pageNum > 0,
      currentUserRole: currentUserRole,
      message: message || 'Users fetched successfully',
      pagination,
      requestId,
      timestamp: new Date().toISOString(),
    };

    const totalTime = Date.now() - startTime;
    console.log('✅ [ORGS-USERS-API] Request completed:', {
      requestId,
      totalTime: totalTime + 'ms',
      usersReturned: response.users.length,
    });

    return NextResponse.json(response);
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [ORGS-USERS-API] Request failed:', {
      requestId,
      error: error.message,
      duration: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch organization users',
        users: [],
        orgUsers: [], // Backward compatibility
        success: false,
        requestId,
        timestamp: new Date().toISOString(),
      },
      { status: error.status || 500 },
    );
  }
}
