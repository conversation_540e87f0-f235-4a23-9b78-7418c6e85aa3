import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';
import { z } from 'zod';
import { invalidateUserRoles } from '@/lib/external-roles';

// Validation schema for update user request
const updateUserSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  role: z.string().min(1, { message: 'Role is required' }),
  orgId: z.string().min(1, { message: 'Organization ID is required' }),
  orgName: z.string().min(1, { message: 'Organization name is required' }),
  accepted: z.boolean().optional(),
  credits: z.number().optional(),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log('🚀 [UPDATE-USER-API] Starting user update request');
  console.log('📊 [UPDATE-USER-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateUserSchema.parse(body);

    console.log('✅ [UPDATE-USER-API] Validation successful:', {
      requestId,
      email: validatedData.email,
      role: validatedData.role,
      orgId: validatedData.orgId,
      orgName: validatedData.orgName,
      accepted: validatedData.accepted,
      credits: validatedData.credits,
    });

    // Get session for auth context
    const session = await getSession();
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', message: 'Authentication required', success: false },
        { status: 401 },
      );
    }

    // Call external API with the correct format
    console.log('📤 [UPDATE-USER-API] Making external API call...');
    const apiRes = await externalClient.post('/orgs/users/update', {
      name: validatedData.orgName, // External API expects 'name' not 'orgId'
      orgId: validatedData.orgId, // Include orgId for future-proofing
      email: validatedData.email,
      role: validatedData.role,
      ...(validatedData.accepted !== undefined && { accepted: validatedData.accepted }),
      ...(validatedData.credits !== undefined && { credits: validatedData.credits }),
    });

    console.log('📥 [UPDATE-USER-API] External API response received:', {
      status: apiRes.status,
      data: apiRes.data,
    });

    // Check if the response indicates success
    const isSuccess =
      apiRes.data?.message === 'User updated' ||
      apiRes.data?.message?.includes('successfully') ||
      (!apiRes.data?.error && apiRes.data?.message);

    if (!isSuccess && apiRes.data?.error) {
      // Handle specific error messages from the external API
      const errorMessage =
        apiRes.data?.message || apiRes.data?.error || 'Failed to update user role';

      if (errorMessage.includes('do not have permission')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'INSUFFICIENT_PERMISSIONS',
          },
          { status: 403 },
        );
      }

      if (errorMessage.includes('Organization not found')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'ORG_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      if (errorMessage.includes('not in organization') || errorMessage.includes('not accepted')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'USER_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      // Generic error
      return NextResponse.json(
        {
          error: errorMessage,
          message: errorMessage,
          success: false,
        },
        { status: 400 },
      );
    }

    // Build successful response
    const response = {
      success: true,
      message: apiRes.data?.message || 'User role updated successfully',
      user: {
        email: validatedData.email,
        role: validatedData.role,
        orgId: validatedData.orgId,
        orgName: validatedData.orgName,
        ...(validatedData.accepted !== undefined && { accepted: validatedData.accepted }),
        ...(validatedData.credits !== undefined && { creditsTransferred: validatedData.credits }),
      },
      updatedBy: session.email,
      updatedAt: new Date().toISOString(),
      requestId,
    };

    const totalTime = Date.now() - startTime;
    console.log('🎉 [UPDATE-USER-API] Request completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      updatedEmail: validatedData.email,
      newRole: validatedData.role,
      orgName: validatedData.orgName,
    });

    // Invalidate Redis cache for the updated user
    try {
      await invalidateUserRoles(validatedData.email);
      console.log('🗑️ [UPDATE-USER-API] Invalidated Redis cache for user:', validatedData.email);
    } catch (cacheError) {
      console.warn('⚠️ [UPDATE-USER-API] Failed to invalidate cache:', cacheError);
      // Continue - cache invalidation failure shouldn't fail the request
    }

    // Add React Query invalidation headers
    const nextResponse = NextResponse.json(response);
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['orgUsers', validatedData.orgId], // Invalidate org users list
          ['userOrgRole', validatedData.email, validatedData.orgId], // Invalidate specific user role
          ['userRole', validatedData.orgId], // Invalidate user role query
          ['permissions'], // Invalidate permissions cache
          ['userOrgRoles', validatedData.email], // Invalidate all org roles for user
        ],
        message: 'User role updated - refresh org users and permissions',
      }),
    );

    return nextResponse;
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [UPDATE-USER-API] Request failed after', totalTime, 'ms', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          message: error.errors[0]?.message || 'Validation failed',
          details: error.errors,
          success: false,
        },
        { status: 400 },
      );
    }

    // Handle external API errors
    if (error.response?.data?.message) {
      const errorMessage = error.response.data.message;

      if (errorMessage.includes('do not have permission')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'INSUFFICIENT_PERMISSIONS',
          },
          { status: 403 },
        );
      }

      if (errorMessage.includes('Organization not found')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'ORG_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      if (errorMessage.includes('not in organization') || errorMessage.includes('not accepted')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'USER_NOT_FOUND',
          },
          { status: 404 },
        );
      }
    }

    return NextResponse.json(
      {
        error: error.message || 'Failed to update user role',
        message: error.message || 'Failed to update user role',
        success: false,
      },
      { status: error.status || 500 },
    );
  }
}
