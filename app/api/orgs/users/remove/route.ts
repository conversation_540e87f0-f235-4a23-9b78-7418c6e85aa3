import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';
import { z } from 'zod';
import { invalidateUserRoles } from '@/lib/external-roles';

// Validation schema for remove user request
const removeUserSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  orgId: z.string().optional(), // Optional for backwards compatibility
  orgName: z.string().min(1, { message: 'Organization name is required' }),
  name: z.string().optional(), // Accept both 'name' and 'orgName' for flexibility
  reason: z.string().optional(),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log('🚀 [REMOVE-USER-API] Starting user removal request');
  console.log('📊 [REMOVE-USER-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = removeUserSchema.parse(body);

    // Use 'name' if provided, otherwise use 'orgName'
    const organizationName = validatedData.name || validatedData.orgName;

    console.log('✅ [REMOVE-USER-API] Validation successful:', {
      requestId,
      email: validatedData.email,
      orgId: validatedData.orgId || 'not provided',
      orgName: organizationName,
      hasReason: !!validatedData.reason,
    });

    // Get session for auth context
    const session = await getSession();
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', message: 'Authentication required', success: false },
        { status: 401 },
      );
    }

    // Allow self-removal when declining invitations (users can remove themselves)
    const isSelfRemoval = session.email === validatedData.email;

    if (isSelfRemoval) {
      console.log('📋 [REMOVE-USER-API] User removing themselves (declining invitation):', {
        requestId,
        email: validatedData.email,
        orgName: organizationName,
        reason: validatedData.reason || 'self-removal',
      });
    }

    // Call external API with the correct format
    console.log('📤 [REMOVE-USER-API] Making external API call...');
    const apiRes = await externalClient.post('/orgs/users/remove', {
      name: organizationName, // External API expects 'name' not 'orgId'
      orgId: validatedData.orgId, // Include orgId for future-proofing
      email: validatedData.email,
    });

    console.log('📥 [REMOVE-USER-API] External API response received:', {
      status: apiRes.status,
      data: apiRes.data,
    });

    // Check if the response indicates success
    const isSuccess =
      apiRes.data?.message === 'User removed' ||
      apiRes.data?.message?.includes('successfully') ||
      (!apiRes.data?.error && apiRes.data?.message);

    if (!isSuccess && apiRes.data?.error) {
      // Handle specific error messages from the external API
      const errorMessage = apiRes.data?.message || apiRes.data?.error || 'Failed to remove user';

      if (errorMessage.includes('do not have permission')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'INSUFFICIENT_PERMISSIONS',
          },
          { status: 403 },
        );
      }

      if (errorMessage.includes('User not found')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'USER_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      if (errorMessage.includes('cannot remove the owner')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'OWNER_REMOVAL_FORBIDDEN',
          },
          { status: 409 },
        );
      }

      // Generic error
      return NextResponse.json(
        {
          error: errorMessage,
          message: errorMessage,
          success: false,
        },
        { status: 400 },
      );
    }

    // Build successful response
    const response = {
      success: true,
      message: apiRes.data?.message || 'User removed successfully',
      user: {
        email: validatedData.email,
        orgId: validatedData.orgId,
        orgName: organizationName,
        removedAt: new Date().toISOString(),
      },
      removedBy: session.email,
      requestId,
    };

    const totalTime = Date.now() - startTime;
    console.log('🎉 [REMOVE-USER-API] Request completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      removedEmail: validatedData.email,
      orgName: organizationName,
    });

    // Invalidate Redis cache for the removed user
    try {
      await invalidateUserRoles(validatedData.email);
      console.log('🗑️ [REMOVE-USER-API] Invalidated Redis cache for user:', validatedData.email);
    } catch (cacheError) {
      console.warn('⚠️ [REMOVE-USER-API] Failed to invalidate cache:', cacheError);
      // Continue - cache invalidation failure shouldn't fail the request
    }

    // Add React Query invalidation headers
    const nextResponse = NextResponse.json(response);
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['orgUsers', validatedData.orgId], // Invalidate org users list
          ['userOrgRole', validatedData.email, validatedData.orgId], // Invalidate specific user role
          ['userRole', validatedData.orgId], // Invalidate user role query
          ['permissions'], // Invalidate permissions cache
          ['userOrgRoles', validatedData.email], // Invalidate all org roles for user
          ['userOrganizations'], // Invalidate user organizations list
        ],
        message: 'User removed - refresh org users and permissions',
      }),
    );

    return nextResponse;
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [REMOVE-USER-API] Request failed after', totalTime, 'ms', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          message: error.errors[0]?.message || 'Validation failed',
          details: error.errors,
          success: false,
        },
        { status: 400 },
      );
    }

    // Handle external API errors
    if (error.response?.data?.message) {
      const errorMessage = error.response.data.message;

      if (errorMessage.includes('do not have permission')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'INSUFFICIENT_PERMISSIONS',
          },
          { status: 403 },
        );
      }

      if (errorMessage.includes('User not found')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'USER_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      if (errorMessage.includes('cannot remove the owner')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: errorMessage,
            success: false,
            code: 'OWNER_REMOVAL_FORBIDDEN',
          },
          { status: 409 },
        );
      }
    }

    return NextResponse.json(
      {
        error: error.message || 'Failed to remove user',
        message: error.message || 'Failed to remove user',
        success: false,
      },
      { status: error.status || 500 },
    );
  }
}
