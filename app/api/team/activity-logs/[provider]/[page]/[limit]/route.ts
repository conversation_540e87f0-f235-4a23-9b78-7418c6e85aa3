import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';

// Sample activity logs data
const SAMPLE_ACTIVITY_LOGS = [
  {
    userEmail: '<EMAIL>',
    user: '<PERSON>',
    userRole: 'Owner',
    action: 'Invite User',
    actionDescription: 'Invited <EMAIL> as User',
    timestamp: '2024-01-15T10:30:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: '<PERSON>',
    userRole: 'SuperAdmin',
    action: 'Change User Role',
    actionDescription: 'Changed <EMAIL> role from Manager to Admin',
    timestamp: '2024-01-15T09:15:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: '<PERSON>',
    userRole: 'Admin',
    action: 'Add New Device',
    actionDescription: 'Added Quantum Device QD-2024-001 to AWS provider',
    timestamp: '2024-01-14T16:45:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: '<PERSON>',
    userRole: 'Manager',
    action: 'Change Device Info',
    actionDescription: 'Updated calibration settings for QD-2023-015',
    timestamp: '2024-01-14T14:30:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: 'Alex Chen',
    userRole: 'Owner',
    action: 'Invite User',
    actionDescription: 'Invited <EMAIL> as User',
    timestamp: '2024-01-14T12:20:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: 'David Lee',
    userRole: 'User',
    action: 'Change Device Info',
    actionDescription: 'Updated quantum circuit parameters for QD-2023-008',
    timestamp: '2024-01-14T11:15:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: 'Sarah Kim',
    userRole: 'SuperAdmin',
    action: 'Add New Device',
    actionDescription: 'Added Quantum Device QD-2024-002 to IBM provider',
    timestamp: '2024-01-13T15:30:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: 'James Wilson',
    userRole: 'Manager',
    action: 'Change User Role',
    actionDescription: 'Changed <EMAIL> permissions for device access',
    timestamp: '2024-01-13T12:15:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: 'Robert Taylor',
    userRole: 'Admin',
    action: 'Delete Device',
    actionDescription: 'Removed deprecated device QD-2022-003 from inventory',
    timestamp: '2024-01-13T10:45:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: 'Jessica Brown',
    userRole: 'User',
    action: 'Change Device Info',
    actionDescription: 'Updated quantum gate fidelity measurements for QD-2023-012',
    timestamp: '2024-01-12T16:20:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: 'Michael Rodriguez',
    userRole: 'Admin',
    action: 'Add New Device',
    actionDescription: 'Added Quantum Device QD-2024-003 to Google provider',
    timestamp: '2024-01-12T13:10:00Z',
  },
  {
    userEmail: '<EMAIL>',
    user: 'Emily Watson',
    userRole: 'Manager',
    action: 'Invite User',
    actionDescription: 'Invited new team member to join quantum research group',
    timestamp: '2024-01-12T09:30:00Z',
  },
];

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ provider: string; page: string; limit: string }> },
) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log('🚀 [ACTIVITY-LOGS-API] Starting activity logs fetch request');
  console.log('📊 [ACTIVITY-LOGS-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    const { provider, page, limit } = await params;
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    console.log('📋 [ACTIVITY-LOGS-API] Request parameters:', {
      requestId,
      provider,
      page: pageNum,
      limit: limitNum,
    });

    // Validate parameters
    if (!provider || isNaN(pageNum) || isNaN(limitNum)) {
      return NextResponse.json(
        { error: 'Invalid parameters. provider, page, and limit are required.' },
        { status: 400 },
      );
    }

    // Get session for auth context
    const session = await getSession();
    console.log('✅ [ACTIVITY-LOGS-API] Session retrieved:', {
      requestId,
      hasSession: !!session,
      userEmail: session?.email || 'N/A',
    });

    // Calculate pagination
    const startIndex = pageNum * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedLogs = SAMPLE_ACTIVITY_LOGS.slice(startIndex, endIndex);
    const totalLogs = SAMPLE_ACTIVITY_LOGS.length;
    const totalPages = Math.ceil(totalLogs / limitNum);

    const response = {
      success: true,
      auditLogsArray: paginatedLogs,
      totalLogs,
      currentPage: pageNum,
      totalPages,
      pageSize: limitNum,
      hasNextPage: pageNum < totalPages - 1,
      hasPreviousPage: pageNum > 0,
      provider,
    };

    // Try external API call with fallback to sample data
    try {
      console.log('📤 [ACTIVITY-LOGS-API] Attempting external API call...');
      const externalApiStart = Date.now();
      const apiRes = await externalClient.get(
        `/audit-logs/${provider}?page=${page}&resultsPerPage=${limit}`,
      );
      const externalApiTime = Date.now() - externalApiStart;

      console.log('📥 [ACTIVITY-LOGS-API] External API response in', externalApiTime, 'ms:', {
        requestId,
        responseSize: JSON.stringify(apiRes.data).length,
        logsCount: apiRes.data?.auditLogsArray?.length || 0,
      });

      // Use external API data if available and valid
      if (apiRes.data?.auditLogsArray && Array.isArray(apiRes.data.auditLogsArray)) {
        response.auditLogsArray = apiRes.data.auditLogsArray;
        response.totalLogs = apiRes.data.totalLogs || apiRes.data.auditLogsArray.length;
        response.totalPages = Math.ceil(response.totalLogs / limitNum);
      }
    } catch (apiError) {
      console.warn('⚠️ [ACTIVITY-LOGS-API] External API failed, using sample data:', {
        requestId,
        error: apiError instanceof Error ? apiError.message : apiError,
      });
    }

    const totalTime = Date.now() - startTime;
    console.log('🎉 [ACTIVITY-LOGS-API] Request fulfilled in', totalTime, 'ms');

    return NextResponse.json(response);
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [ACTIVITY-LOGS-API] Request failed after', totalTime, 'ms');
    console.error('🚨 [ACTIVITY-LOGS-API] Error details:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : JSON.stringify(error),
      stackTrace: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      totalDuration: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: 'Failed to fetch activity logs',
        requestId,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
