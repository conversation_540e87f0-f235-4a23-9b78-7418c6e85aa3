import { NextRequest, NextResponse } from 'next/server';
import { invalidateUserRoles, updateUserRoleInOrg, removeUserFromOrg } from '@/lib/external-roles';

/**
 * Enhanced webhook endpoint for external role updates
 * Supports both legacy flat roles and new org-specific role updates
 */
export async function POST(request: NextRequest) {
  try {
    // Verify webhook signature or API key for security
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.WEBHOOK_SECRET_TOKEN;

    if (!expectedToken || authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      email,
      roles,
      action,
      orgId,
      orgName,
      newRole,
      // Legacy support
      orgRoles,
    } = body;

    if (!email || typeof email !== 'string') {
      return NextResponse.json(
        { error: 'Email is required and must be a string' },
        { status: 400 },
      );
    }

    console.log(`🔔 [WEBHOOK] Processing role update for ${email}:`, {
      action,
      orgId,
      newRole,
      hasOrgRoles: !!orgRoles,
      hasLegacyRoles: !!roles,
    });

    // Handle different webhook actions
    switch (action) {
      case 'delete':
      case 'invalidate':
        // Invalidate all cached roles (both legacy and org-specific)
        await invalidateUserRoles(email);
        console.log(`🗑️ [WEBHOOK] Invalidated all roles for ${email}`);

        const invalidateResponse = NextResponse.json({
          success: true,
          message: `All roles invalidated for ${email}`,
          data: { email, action },
        });

        // Add React Query invalidation headers
        invalidateResponse.headers.set(
          'X-Invalidate-Queries',
          JSON.stringify({
            queries: [
              ['permissions'], // Invalidate permissions cache
              ['userOrgRoles', email], // Invalidate all org roles for user
            ],
            message: 'User roles invalidated - refresh all caches',
          }),
        );

        return invalidateResponse;

      case 'update-org-role':
        // Update role in specific organization
        if (!orgId || !newRole) {
          return NextResponse.json(
            { error: 'orgId and newRole are required for update-org-role action' },
            { status: 400 },
          );
        }

        await updateUserRoleInOrg(email, orgId, newRole, orgName);
        console.log(`🔄 [WEBHOOK] Updated role for ${email} in org ${orgId}: ${newRole}`);

        const updateOrgResponse = NextResponse.json({
          success: true,
          message: `Role updated for ${email} in organization ${orgId}`,
          data: { email, orgId, newRole, orgName },
        });

        // Add React Query invalidation headers
        updateOrgResponse.headers.set(
          'X-Invalidate-Queries',
          JSON.stringify({
            queries: [
              ['userOrgRole', email, orgId], // Invalidate specific user role
              ['userOrgRoles', email], // Invalidate all org roles for user
              ['orgUsers', orgId], // Invalidate org users list
              ['permissions'], // Invalidate permissions cache
            ],
            message: 'User role updated via webhook - refresh caches',
          }),
        );

        return updateOrgResponse;

      case 'remove-from-org':
        // Remove user from specific organization
        if (!orgId) {
          return NextResponse.json(
            { error: 'orgId is required for remove-from-org action' },
            { status: 400 },
          );
        }

        await removeUserFromOrg(email, orgId);
        console.log(`🗑️ [WEBHOOK] Removed ${email} from org ${orgId}`);

        const removeResponse = NextResponse.json({
          success: true,
          message: `User ${email} removed from organization ${orgId}`,
          data: { email, orgId },
        });

        // Add React Query invalidation headers
        removeResponse.headers.set(
          'X-Invalidate-Queries',
          JSON.stringify({
            queries: [
              ['userOrgRole', email, orgId], // Invalidate specific user role
              ['userOrgRoles', email], // Invalidate all org roles for user
              ['orgUsers', orgId], // Invalidate org users list
              ['permissions'], // Invalidate permissions cache
            ],
            message: 'User removed from org via webhook - refresh caches',
          }),
        );

        return removeResponse;

      case 'sync-org-roles':
        // Sync all org roles for user (bulk update)
        if (!orgRoles || typeof orgRoles !== 'object') {
          return NextResponse.json(
            { error: 'orgRoles object is required for sync-org-roles action' },
            { status: 400 },
          );
        }

        // Update each organization role
        const updatePromises = Object.entries(orgRoles).map(
          async ([id, roleData]: [string, any]) => {
            await updateUserRoleInOrg(email, id, roleData.role, roleData.orgName);
          },
        );

        await Promise.all(updatePromises);
        console.log(`🔄 [WEBHOOK] Synced ${Object.keys(orgRoles).length} org roles for ${email}`);

        const syncResponse = NextResponse.json({
          success: true,
          message: `Synced ${Object.keys(orgRoles).length} organization roles for ${email}`,
          data: { email, orgRoles: Object.keys(orgRoles) },
        });

        // Add React Query invalidation headers
        syncResponse.headers.set(
          'X-Invalidate-Queries',
          JSON.stringify({
            queries: [
              ['userOrgRoles', email], // Invalidate all org roles for user
              ...Object.keys(orgRoles).map((orgId) => ['orgUsers', orgId]), // Invalidate org users lists for all synced orgs
              ['permissions'], // Invalidate permissions cache
            ],
            message: 'Organization roles synced via webhook - refresh caches',
          }),
        );

        return syncResponse;

      default:
        return NextResponse.json(
          {
            error:
              'Invalid action. Use update-org-role, remove-from-org, sync-org-roles, or invalidate',
          },
          { status: 400 },
        );
    }
  } catch (error) {
    console.error('❌ [WEBHOOK] Role update failed:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * GET endpoint to show webhook documentation and examples
 */
export async function GET() {
  return NextResponse.json({
    service: 'Enhanced Role Update Webhook',
    description: 'Webhook endpoint for external role updates with organization-specific support',
    authentication: 'Bearer token in Authorization header',
    endpoints: {
      'POST /api/update-roles': 'Update user roles',
    },
    actions: {
      'update-org-role': {
        description: 'Update user role in specific organization',
        required: ['email', 'orgId', 'newRole'],
        optional: ['orgName'],
        example: {
          email: '<EMAIL>',
          action: 'update-org-role',
          orgId: 'org123',
          newRole: 'admin',
          orgName: 'Example Organization',
        },
      },
      'remove-from-org': {
        description: 'Remove user from specific organization',
        required: ['email', 'orgId'],
        example: {
          email: '<EMAIL>',
          action: 'remove-from-org',
          orgId: 'org123',
        },
      },
      'sync-org-roles': {
        description: 'Sync all organization roles for user',
        required: ['email', 'orgRoles'],
        example: {
          email: '<EMAIL>',
          action: 'sync-org-roles',
          orgRoles: {
            org123: { role: 'admin', orgName: 'Example Org' },
            org456: { role: 'member', orgName: 'Another Org' },
          },
        },
      },
      invalidate: {
        description: 'Invalidate all cached roles',
        required: ['email'],
        example: {
          email: '<EMAIL>',
          action: 'invalidate',
        },
      },
    },
    timestamp: new Date().toISOString(),
  });
}
