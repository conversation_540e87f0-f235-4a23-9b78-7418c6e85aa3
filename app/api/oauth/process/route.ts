import { NextRequest, NextResponse } from 'next/server';
import { createOAuthSession } from '@/app/(auth)/actions';

export async function POST(request: NextRequest) {
  try {
    const { accessToken, idToken } = await request.json();

    if (!accessToken || !idToken) {
      return NextResponse.json({ success: false, error: 'Missing OAuth tokens' }, { status: 400 });
    }

    // Parse user info from ID token (server-side)
    const userInfo = parseIdToken(idToken);
    if (!userInfo) {
      return NextResponse.json(
        { success: false, error: 'Invalid ID token format' },
        { status: 400 },
      );
    }

    console.log('🎯 [OAUTH-API] Processing OAuth tokens for user:', {
      email: userInfo.email,
      userId: userInfo.sub,
      name: userInfo.name,
    });

    // Create session using existing server action
    const result = await createOAuthSession({
      userId: userInfo.sub,
      email: userInfo.email,
      username: userInfo['cognito:username'],
      tokens: {
        accessToken,
        idToken,
      },
    });

    if (!result.success) {
      console.error('❌ [OAUTH-API] Session creation failed:', result.error);
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to create session' },
        { status: 500 },
      );
    }

    console.log('✅ [OAUTH-API] OAuth session created successfully');

    return NextResponse.json({
      success: true,
      redirectTo: result.redirectTo || '/',
    });
  } catch (error) {
    console.error('❌ [OAUTH-API] Server error:', error);
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
  }
}

function parseIdToken(idToken: string) {
  try {
    const payload = idToken.split('.')[1];
    return JSON.parse(Buffer.from(payload, 'base64').toString());
  } catch (error) {
    console.error('❌ [OAUTH-API] Failed to parse ID token:', error);
    return null;
  }
}
