import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';

export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const deviceId = searchParams.get('id');
    const body = await request.json();

    if (!deviceId) {
      return NextResponse.json(
        {
          error: 'Device ID is required',
        },
        { status: 400 },
      );
    }

    // Proxy to external API
    const response = await externalClient.patch(`/quantum-devices/${deviceId}`, body);

    // Add React Query invalidation headers
    const nextResponse = NextResponse.json(response.data);
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['quantumDevice', deviceId], // Invalidate specific device cache
          ['quantumDevices'], // Invalidate devices list
          ['devices'], // Alternative devices cache key
        ],
        message: 'Quantum device updated - refresh device data',
      }),
    );

    return nextResponse;
  } catch (error: any) {
    console.error('❌ [API Gateway] Error updating quantum device:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to update quantum device',
      },
      { status: error.status || 500 },
    );
  }
}
