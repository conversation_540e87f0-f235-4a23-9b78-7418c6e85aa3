import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';

// Sample quantum devices data
const SAMPLE_DEVICES = [
  {
    qbraidDeviceId: 'aws_braket_sv1',
    name: 'SV1 State Vector Simulator',
    provider: 'AWS',
    type: 'Simulator',
    status: 'ONLINE',
    isAvailable: true,
    qubits: 34,
    topology: 'fully_connected',
    basisGates: ['x', 'y', 'z', 'h', 'cnot', 'ccnot', 'rx', 'ry', 'rz'],
    description: 'High-performance state vector simulator',
    pricing: { perShot: 0.075, perMinute: 0.0 },
    queue: { running: 0, queued: 0 },
  },
  {
    qbraidDeviceId: 'aws_braket_dm1',
    name: 'DM1 Density Matrix Simulator',
    provider: 'AWS',
    type: 'Simulator',
    status: 'ONLINE',
    isAvailable: true,
    qubits: 17,
    topology: 'fully_connected',
    basisGates: ['x', 'y', 'z', 'h', 'cnot', 'rx', 'ry', 'rz'],
    description: 'Density matrix simulator with noise modeling',
    pricing: { perShot: 0.25, perMinute: 0.0 },
    queue: { running: 2, queued: 15 },
  },
  {
    qbraidDeviceId: 'aws_braket_rigetti_aspen_m3',
    name: 'Aspen-M-3',
    provider: 'Rigetti',
    type: 'QPU',
    status: 'ONLINE',
    isAvailable: false,
    qubits: 80,
    topology: 'octagonal',
    basisGates: ['rx', 'rz', 'cz'],
    description: '80-qubit superconducting QPU',
    pricing: { perShot: 0.0, perMinute: 0.3 },
    queue: { running: 5, queued: 127 },
  },
  {
    qbraidDeviceId: 'ibm_brisbane',
    name: 'IBM Brisbane',
    provider: 'IBM',
    type: 'QPU',
    status: 'ONLINE',
    isAvailable: true,
    qubits: 127,
    topology: 'heavy_hex',
    basisGates: ['id', 'rz', 'sx', 'x', 'cx', 'reset'],
    description: '127-qubit IBM Quantum processor',
    pricing: { perShot: 1.6, perMinute: 0.0 },
    queue: { running: 3, queued: 89 },
  },
  {
    qbraidDeviceId: 'ibm_simulator_statevector',
    name: 'IBM Qasm Simulator',
    provider: 'IBM',
    type: 'Simulator',
    status: 'ONLINE',
    isAvailable: true,
    qubits: 32,
    topology: 'fully_connected',
    basisGates: ['u1', 'u2', 'u3', 'cx', 'id', 'snapshot'],
    description: 'Classical simulator of quantum circuits',
    pricing: { perShot: 0.0, perMinute: 0.0 },
    queue: { running: 0, queued: 0 },
  },
  {
    qbraidDeviceId: 'ionq_harmony',
    name: 'IonQ Harmony',
    provider: 'IonQ',
    type: 'QPU',
    status: 'ONLINE',
    isAvailable: true,
    qubits: 11,
    topology: 'fully_connected',
    basisGates: ['gpi', 'gpi2', 'ms'],
    description: '11-qubit trapped ion quantum computer',
    pricing: { perShot: 0.01, perMinute: 0.0 },
    queue: { running: 1, queued: 23 },
  },
  {
    qbraidDeviceId: 'ionq_aria1',
    name: 'IonQ Aria-1',
    provider: 'IonQ',
    type: 'QPU',
    status: 'OFFLINE',
    isAvailable: false,
    qubits: 25,
    topology: 'fully_connected',
    basisGates: ['gpi', 'gpi2', 'ms'],
    description: '25-qubit trapped ion quantum computer',
    pricing: { perShot: 0.22, perMinute: 0.0 },
    queue: { running: 0, queued: 0 },
  },
  {
    qbraidDeviceId: 'quera_aquila',
    name: 'QuEra Aquila',
    provider: 'QuEra',
    type: 'QPU',
    status: 'ONLINE',
    isAvailable: false,
    qubits: 256,
    topology: 'neutral_atom',
    basisGates: ['rydberg_interaction'],
    description: '256-qubit neutral atom quantum simulator',
    pricing: { perShot: 0.1, perMinute: 1.0 },
    queue: { running: 2, queued: 45 },
  },
  {
    qbraidDeviceId: 'oqc_lucy',
    name: 'OQC Lucy',
    provider: 'OQC',
    type: 'QPU',
    status: 'RETIRED',
    isAvailable: false,
    qubits: 8,
    topology: 'ring',
    basisGates: ['rz', 'sx', 'x', 'ecr'],
    description: '8-qubit superconducting QPU (retired)',
    pricing: { perShot: 0.0, perMinute: 0.0 },
    queue: { running: 0, queued: 0 },
  },
  {
    qbraidDeviceId: 'qbraid_qasm_simulator',
    name: 'qBraid QASM Simulator',
    provider: 'qBraid',
    type: 'Simulator',
    status: 'ONLINE',
    isAvailable: true,
    qubits: 25,
    topology: 'fully_connected',
    basisGates: ['u', 'cx'],
    description: 'Fast cloud-based QASM simulator',
    pricing: { perShot: 0.0, perMinute: 0.0 },
    queue: { running: 0, queued: 0 },
  },
];

// Uncomment below to use sample data instead of external API
/*
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const qbraidId = searchParams.get('qbraid_id');
  const provider = searchParams.get('provider');
  const type = searchParams.get('type');
  const status = searchParams.get('status');
  const isAvailable = searchParams.get('isAvailable');

  try {
    if (qbraidId) {
      // Fetch single device by ID
      const device = SAMPLE_DEVICES.find((d) => d.qbraidDeviceId === qbraidId);
      if (!device) {
        return NextResponse.json({ error: 'Device not found' }, { status: 404 });
      }
      return NextResponse.json(device);
    } else {
      // Fetch all devices with optional filtering
      let filteredDevices = SAMPLE_DEVICES;

      // Apply filters
      if (provider) {
        filteredDevices = filteredDevices.filter(
          (d) => d.provider.toLowerCase() === provider.toLowerCase(),
        );
      }

      if (type) {
        filteredDevices = filteredDevices.filter(
          (d) => d.type.toLowerCase() === type.toLowerCase(),
        );
      }

      if (status) {
        filteredDevices = filteredDevices.filter(
          (d) => d.status.toLowerCase() === status.toLowerCase(),
        );
      }

      if (isAvailable !== null && isAvailable !== undefined) {
        const availabilityFilter = isAvailable.toLowerCase() === 'true';
        filteredDevices = filteredDevices.filter((d) => d.isAvailable === availabilityFilter);
      }

      return NextResponse.json(filteredDevices);
    }
  } catch (error: any) {
    console.error('❌ [API Gateway] Error fetching quantum devices:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch quantum devices',
        data: qbraidId ? {} : [],
      },
      { status: error.status || 500 },
    );
  }
}
*/

// Uncomment below to use external API instead of sample data

export async function GET(request: NextRequest) {
  // Extract query parameters from incoming request
  const { searchParams } = new URL(request.url);

  // Build params object dynamically for external API call
  const params: Record<string, string> = {};
  const supportedParams = ['qbraid_id', 'provider', 'type', 'status', 'isAvailable'];
  supportedParams.forEach((key) => {
    const value = searchParams.get(key);
    if (value !== null) {
      params[key] = value;
    }
  });

  try {
    // Proxy request to external QBraid API with any provided filters
    const response = await externalClient.get('/quantum-devices', params);
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('❌ [API Gateway] Error fetching quantum devices:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch quantum devices',
        data: params.qbraid_id ? {} : [],
      },
      { status: error.status || 500 },
    );
  }
}
