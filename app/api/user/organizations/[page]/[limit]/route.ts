import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';

/**
 * GET /api/user/organizations/[page]/[limit]
 * Fetch user's organizations with pagination
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ page: string; limit: string }> },
) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Await params before accessing properties
    const { page: pageParam, limit: limitParam } = await params;
    const page = parseInt(pageParam) || 0;
    const limit = parseInt(limitParam) || 10;

    console.log(
      `📋 [USER-ORGS-API] Fetching organizations for ${session.email} (page: ${page}, limit: ${limit})`,
    );

    try {
      // Call external API
      const response = await externalClient.get(`/orgs/get/${page}/${limit}`);

      console.log('✅ [USER-ORGS-API] External API response:', {
        status: response.status,
        organizationCount: response.data?.organizations?.length || 0,
      });

      // Transform the response to match our frontend expectations
      const transformedOrgs =
        response.data?.organizations?.map((item: any) => ({
          orgId: item.org?.organization?._id || item.org?._id,
          orgName: item.org?.organization?.name || 'Unknown Organization',
          role: item.org?.role || 'member',
          accepted: item.org?.accepted || false,
          invited: item.org?.invited || false,
          description: item.org?.organization?.description || '',
          email: item.org?.organization?.orgEmail || '',
          credits: item.orgBilling?.qBraidCredits || 0,
          devices: item.org?.organization?.devices || [],
          joinedAt: item.org?.createdAt || new Date().toISOString(),
        })) || [];

      return NextResponse.json({
        success: true,
        organizations: transformedOrgs,
        pagination: response.data?.pagination || {
          currentPage: page,
          limit: limit,
          totalPages: 1,
          totalOrganizations: transformedOrgs.length,
        },
      });
    } catch (error: any) {
      console.error(
        '❌ [USER-ORGS-API] External API error:',
        error.response?.data || error.message,
      );

      // Return empty organizations array on error
      return NextResponse.json({
        success: true,
        organizations: [],
        pagination: {
          currentPage: page,
          limit: limit,
          totalPages: 0,
          totalOrganizations: 0,
        },
        error: 'Failed to fetch organizations',
      });
    }
  } catch (error) {
    console.error('❌ [USER-ORGS-API] Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
