import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';
import type { UserProfile } from '@/types/user';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log('🚀 [USER-PROFILE-API] Starting user profile fetch request');
  console.log('📊 [USER-PROFILE-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
    userAgent: request.headers.get('user-agent')?.substring(0, 50) + '...',
    referer: request.headers.get('referer') || 'N/A',
  });

  try {
    // Get session for user identification
    const sessionStart = Date.now();
    const session = await getSession();
    const sessionTime = Date.now() - sessionStart;

    console.log('✅ [USER-PROFILE-API] Session retrieved:', {
      requestId,
      hasSession: !!session,
      userEmail: session?.email || 'N/A',
      userId: session?.userId || 'N/A',
      sessionRetrievalTime: sessionTime + 'ms',
    });

    // Require authentication
    if (!session?.email) {
      console.error('❌ [USER-PROFILE-API] Authentication failed:', {
        requestId,
        reason: 'No session or email found',
        hasSession: !!session,
        sessionKeys: session ? Object.keys(session) : [],
      });
      return NextResponse.json({ error: 'Authentication required', requestId }, { status: 401 });
    }

    // Call external API (no fallback to sample data)
    console.log('📤 [USER-PROFILE-API] Calling external API...');
    const externalApiStart = Date.now();
    const apiRes = await externalClient.get('/get-user-info');
    const externalApiTime = Date.now() - externalApiStart;

    console.log('📥 [USER-PROFILE-API] External API response received:', {
      requestId,
      responseTime: externalApiTime + 'ms',
      responseStatus: apiRes.status || 'unknown',
      responseSize: JSON.stringify(apiRes.data).length + ' bytes',
      hasProfile: !!apiRes.data,
      dataKeys: apiRes.data ? Object.keys(apiRes.data) : [],
    });

    // Validate external API response
    if (!apiRes.data || typeof apiRes.data !== 'object') {
      console.error('❌ [USER-PROFILE-API] External API returned invalid data:', {
        requestId,
        hasData: !!apiRes.data,
        dataType: typeof apiRes.data,
      });
      return NextResponse.json(
        {
          error: 'Failed to fetch user profile from external API',
          requestId,
          success: false,
        },
        { status: 500 },
      );
    }

    console.log('✅ [USER-PROFILE-API] Using external API data:', {
      requestId,
      firstName: apiRes.data.firstName,
      lastName: apiRes.data.lastName,
      email: apiRes.data.email,
      hasPersonalInfo: !!apiRes.data.personalInformation,
    });

    const response = {
      success: true,
      profile: apiRes.data,
      requestId,
      timestamp: new Date().toISOString(),
    };

    // Final response logging
    const totalTime = Date.now() - startTime;
    const responseSize = JSON.stringify(response).length;

    console.log('🎉 [USER-PROFILE-API] Request completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      dataSource: 'external-api',
      responseSize: responseSize + ' bytes',
      profileEmail: response.profile.email,
      firstName: response.profile.firstName,
      lastName: response.profile.lastName,
      hasPersonalInfo: !!response.profile.personalInformation,
      sessionTime: sessionTime + 'ms',
      processingTime: totalTime - sessionTime + 'ms',
    });

    return NextResponse.json(response);
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [USER-PROFILE-API] Request failed after', totalTime, 'ms');
    console.error('🚨 [USER-PROFILE-API] Error details:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : JSON.stringify(error),
      stackTrace: error instanceof Error ? error.stack?.split('\n').slice(0, 5) : undefined,
      timestamp: new Date().toISOString(),
      totalDuration: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: 'Failed to fetch user profile',
        requestId,
        timestamp: new Date().toISOString(),
        success: false,
      },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log('🚀 [USER-PROFILE-API] Starting profile update request');
  console.log('📊 [USER-PROFILE-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
    userAgent: request.headers.get('user-agent')?.substring(0, 50) + '...',
    contentType: request.headers.get('content-type'),
    contentLength: request.headers.get('content-length'),
  });

  try {
    // Parse request body
    const bodyParseStart = Date.now();
    const body = await request.json();
    const bodyParseTime = Date.now() - bodyParseStart;

    console.log('📋 [USER-PROFILE-API] Update body parsed:', {
      requestId,
      parseTime: bodyParseTime + 'ms',
      bodySize: JSON.stringify(body).length + ' bytes',
      updatedFields: Object.keys(body),
      hasPersonalInfo: !!body.personalInformation,
    });

    // Get session
    const sessionStart = Date.now();
    const session = await getSession();
    const sessionTime = Date.now() - sessionStart;

    console.log('✅ [USER-PROFILE-API] Session for update:', {
      requestId,
      sessionTime: sessionTime + 'ms',
      userEmail: session?.email || 'N/A',
    });

    if (!session?.email) {
      console.error('❌ [USER-PROFILE-API] Authentication failed for update:', { requestId });
      return NextResponse.json({ error: 'Authentication required', requestId }, { status: 401 });
    }

    // Validate updatable fields - only allow specific fields
    const allowedFields = [
      'firstName',
      'lastName',
      'personalInformation',
      // Add other allowed fields as needed
    ];

    const updateData: Partial<UserProfile> = {};
    const updatedFields: string[] = [];

    for (const [key, value] of Object.entries(body)) {
      if (allowedFields.includes(key)) {
        (updateData as any)[key] = value;
        updatedFields.push(key);
      }
    }

    console.log('🔒 [USER-PROFILE-API] Validated update fields:', {
      requestId,
      updatedFields,
      totalFields: updatedFields.length,
      ignoredFields: Object.keys(body).filter((key) => !allowedFields.includes(key)),
    });

    if (updatedFields.length === 0) {
      console.warn('⚠️ [USER-PROFILE-API] No valid fields to update:', { requestId });
      return NextResponse.json(
        { success: true, message: 'No changes to update', requestId },
        { status: 200 },
      );
    }

    const response = {
      success: true,
      message: 'Profile updated successfully',
      updatedFields,
      requestId,
      timestamp: new Date().toISOString(),
    };

    // Try external API update
    let updateSource = 'mock-update';
    try {
      console.log('📤 [USER-PROFILE-API] Attempting external update call...');
      const externalStart = Date.now();

      // Format the request body according to the API spec
      const requestBody = {
        fieldsToAssign: updateData,
      };

      const apiRes = await externalClient.put('/edit-user-personal-info', requestBody);
      const externalTime = Date.now() - externalStart;

      console.log('📥 [USER-PROFILE-API] External update response:', {
        requestId,
        time: externalTime + 'ms',
        status: apiRes.status,
        success: apiRes.data?.success || false,
        message: apiRes.data?.message || 'No message',
      });

      if (apiRes.data?.success) {
        Object.assign(response, {
          message: apiRes.data.message || 'Profile updated successfully',
          user: apiRes.data.user,
        });
        updateSource = 'external-api';
      }
    } catch (apiError: any) {
      console.error('❌ [USER-PROFILE-API] External update failed:', {
        requestId,
        error: apiError.message,
        status: apiError.status || 'unknown',
        endpoint: '/edit-user-personal-info',
      });

      // Return error instead of mock for failed updates
      return NextResponse.json(
        {
          error: 'Failed to update profile in external system',
          requestId,
          timestamp: new Date().toISOString(),
          success: false,
        },
        { status: 500 },
      );
    }

    const totalTime = Date.now() - startTime;
    console.log('🎉 [USER-PROFILE-API] Update completed:', {
      requestId,
      totalTime: totalTime + 'ms',
      updateSource,
      updatedFields,
      sessionTime: sessionTime + 'ms',
    });

    // Add React Query invalidation headers
    const nextResponse = NextResponse.json(response);
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['userProfile'], // Invalidate user profile cache
          ['profile'], // Alternative profile cache key
        ],
        message: 'User profile updated - refresh profile data',
      }),
    );

    return nextResponse;
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [USER-PROFILE-API] Update failed:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      totalTime: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: 'Failed to update profile',
        requestId,
        timestamp: new Date().toISOString(),
        success: false,
      },
      { status: 500 },
    );
  }
}
