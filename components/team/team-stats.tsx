import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Users, UserPlus, UserX, Shield } from 'lucide-react';

type Stats = {
  totalUsers: number;
  activeUsers: number;
  pendingInvites: number;
  totalRoles: number;
};

type TeamStatsProps = {
  stats: Stats;
  isLoading: boolean;
};

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: any;
  gradient: string;
  isLoading?: boolean;
}

const StatsCard = ({ title, value, icon: Icon, gradient, isLoading }: StatsCardProps) => (
  <Card className="bg-[#262131] border-[#3b3b3b] overflow-hidden hover:bg-[#2a2a3e] transition-colors">
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-300 text-base font-semibold">{title}</p>
          {isLoading ? (
            <Skeleton className="h-10 w-20 mt-3 bg-[#3b3b3b]" />
          ) : (
            <p className="text-3xl font-black text-white mt-3">{value}</p>
          )}
        </div>
        <div className={`p-4 rounded-full ${gradient} shadow-lg`}>
          <Icon className="w-7 h-7 text-white" />
        </div>
      </div>
    </CardContent>
  </Card>
);

export function TeamStats({ stats, isLoading }: TeamStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="Total Members"
        value={stats.totalUsers}
        icon={Users}
        gradient="bg-gradient-to-r from-blue-600 to-blue-700"
        isLoading={isLoading}
      />
      <StatsCard
        title="Active Members"
        value={stats.activeUsers}
        icon={UserPlus}
        gradient="bg-gradient-to-r from-green-600 to-green-700"
        isLoading={isLoading}
      />
      <StatsCard
        title="Pending Invites"
        value={stats.pendingInvites}
        icon={UserX}
        gradient="bg-gradient-to-r from-yellow-600 to-yellow-700"
        isLoading={isLoading}
      />
      <StatsCard
        title="Active Roles"
        value={stats.totalRoles}
        icon={Shield}
        gradient="bg-gradient-to-r from-purple-600 to-purple-700"
        isLoading={isLoading}
      />
    </div>
  );
}
