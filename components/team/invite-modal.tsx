'use client';

import { useState } from 'react';
import { z } from 'zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import {
  UserPlus,
  Users,
  Mail,
  Loader2,
  AlertCircle,
  CheckCircle2,
  Plus,
  Trash2,
  Crown,
  ShieldCheck,
  Eye,
  ChevronDown,
  Sparkles,
  User,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Enhanced role definitions
const ROLES = [
  {
    id: 'owner',
    name: 'Owner',
    description: 'Full control',
    icon: Crown,
    color: 'from-amber-500 to-orange-600',
    bgColor: 'bg-amber-500/10',
    borderColor: 'border-amber-500/20',
    permissions: [
      'Full administrative control',
      'Manage billing & payments',
      'Delete organization',
      'Transfer ownership',
    ],
  },
  {
    id: 'admin',
    name: 'Admin',
    description: 'Team management',
    icon: ShieldCheck,
    color: 'from-violet-500 to-purple-600',
    bgColor: 'bg-violet-500/10',
    borderColor: 'border-violet-500/20',
    permissions: [
      'Manage team members',
      'Configure settings',
      'View all data',
      'Cannot delete org',
    ],
  },
  {
    id: 'member',
    name: 'Member',
    description: 'Standard access',
    icon: User,
    color: 'from-blue-500 to-cyan-600',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20',
    permissions: ['Use devices', 'Submit jobs', 'View own data', 'Team collaboration'],
  },
  {
    id: 'viewer',
    name: 'Viewer',
    description: 'Read-only',
    icon: Eye,
    color: 'from-emerald-500 to-green-600',
    bgColor: 'bg-emerald-500/10',
    borderColor: 'border-emerald-500/20',
    permissions: ['View resources', 'Read analytics', 'Cannot modify', 'Cannot submit jobs'],
  },
];

// Form schemas
const singleInviteSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.string().min(1, 'Please select a role'),
});

const multipleInviteSchema = z.object({
  invites: z
    .array(
      z.object({
        email: z.string().email('Invalid email address'),
        role: z.string().min(1, 'Please select a role'),
      }),
    )
    .min(1, 'Add at least one user'),
});

const bulkInviteSchema = z.object({
  emails: z.string().min(1, 'Please enter at least one email'),
  role: z.string().min(1, 'Please select a role'),
});

type SingleInviteForm = z.infer<typeof singleInviteSchema>;
type MultipleInviteForm = z.infer<typeof multipleInviteSchema>;
type BulkInviteForm = z.infer<typeof bulkInviteSchema>;

interface InviteModalProps {
  open: boolean;
  onClose: () => void;
  onInvite: (
    email: string,
    role: string,
  ) => Promise<{ message: string; currentUserRole?: string | null; error?: boolean }>;
  orgName?: string;
  isLoading?: boolean;
}

export function InviteModal({
  open,
  onClose,
  onInvite,
  orgName,
  isLoading = false,
}: InviteModalProps) {
  const [activeTab, setActiveTab] = useState('multiple');
  const [inviteResults, setInviteResults] = useState<
    Array<{
      email: string;
      status: 'success' | 'error';
      message?: string;
      role?: string;
    }>
  >([]);
  const [showRoleDetails, setShowRoleDetails] = useState(false);

  // Single invite form
  const singleForm = useForm<SingleInviteForm>({
    resolver: zodResolver(singleInviteSchema),
    defaultValues: {
      email: '',
      role: '',
    },
  });

  // Multiple invite form with dynamic fields
  const multipleForm = useForm<MultipleInviteForm>({
    resolver: zodResolver(multipleInviteSchema),
    defaultValues: {
      invites: [{ email: '', role: '' }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: multipleForm.control,
    name: 'invites',
  });

  // Bulk invite form
  const bulkForm = useForm<BulkInviteForm>({
    resolver: zodResolver(bulkInviteSchema),
    defaultValues: {
      emails: '',
      role: '',
    },
  });

  // Parse bulk emails
  const parseBulkEmails = (emailText: string): string[] => {
    return emailText
      .split(/[,\n]/)
      .map((email) => email.trim())
      .filter((email) => email && email.includes('@'));
  };

  // Handle single invite
  const handleSingleInvite = async (data: SingleInviteForm) => {
    setInviteResults([]);
    const res = await onInvite(data.email, data.role);
    if (!res.error) {
      setInviteResults([
        {
          email: data.email,
          status: 'success',
          message: res.message,
          role: res.currentUserRole || '',
        },
      ]);
      singleForm.reset();
    } else {
      setInviteResults([
        {
          email: data.email,
          status: 'error',
          message: res.message,
        },
      ]);
    }
  };

  // Handle multiple invites
  const handleMultipleInvite = async (data: MultipleInviteForm) => {
    setInviteResults([]);
    const results: typeof inviteResults = [];
    for (const invite of data.invites) {
      const res = await onInvite(invite.email, invite.role);
      if (!res.error) {
        results.push({
          email: invite.email,
          status: 'success',
          message: res.message,
          role: res.currentUserRole || '',
        });
      } else {
        results.push({
          email: invite.email,
          status: 'error',
          message: res.message,
        });
      }
    }
    setInviteResults(results);
    if (results.every((r) => r.status === 'success')) {
      multipleForm.reset();
    }
  };

  // Handle bulk invite
  const handleBulkInvite = async (data: BulkInviteForm) => {
    const emails = parseBulkEmails(data.emails);
    const results: typeof inviteResults = [];
    for (const email of emails) {
      const res = await onInvite(email, data.role);
      if (!res.error) {
        results.push({
          email,
          status: 'success',
          message: res.message,
          role: res.currentUserRole || '',
        });
      } else {
        results.push({
          email,
          status: 'error',
          message: res.message,
        });
      }
    }
    setInviteResults(results);
    if (results.every((r) => r.status === 'success')) {
      bulkForm.reset();
    }
  };

  // Reset forms and results when closing
  const handleClose = () => {
    singleForm.reset();
    multipleForm.reset();
    bulkForm.reset();
    setInviteResults([]);
    setShowRoleDetails(false);
    onClose();
  };

  // Custom role selector with better UI
  const RoleSelector = ({
    value,
    onChange,
    disabled,
  }: {
    value: string;
    onChange: (value: string) => void;
    disabled?: boolean;
  }) => (
    <Select onValueChange={onChange} value={value} disabled={disabled}>
      <SelectTrigger className="h-12 bg-[#0a0a0f] border border-white/5 text-white hover:border-white/10 focus:border-violet-500 focus:ring-1 focus:ring-violet-500/20 transition-all">
        <SelectValue placeholder="Select a role" />
      </SelectTrigger>
      <SelectContent className="bg-[#0a0a0f] border border-white/10 shadow-2xl">
        {ROLES.map((role) => (
          <SelectItem
            key={role.id}
            value={role.id}
            className="text-gray-300 hover:bg-white/5 hover:text-white focus:bg-white/5 focus:text-white cursor-pointer transition-colors py-3"
          >
            <div className="flex items-center gap-3">
              <div className={cn('p-1.5 rounded-lg bg-gradient-to-br', role.color)}>
                <role.icon className="h-3.5 w-3.5 text-white" />
              </div>
              <div className="flex flex-col">
                <span className="font-medium">{role.name}</span>
                <span className="text-xs text-gray-500">{role.description}</span>
              </div>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl bg-[#0a0a0f] border border-white/10 shadow-2xl modal-scale-in modal-scrollbar overflow-y-auto">
        {/* Header with gradient background */}
        <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-violet-600/10 via-violet-600/5 to-transparent pointer-events-none" />

        <DialogHeader className="relative pb-2">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl shadow-lg shadow-violet-500/20 pulse-soft">
              <Users className="w-6 h-6 text-white" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold text-white">
                Invite Team Members
              </DialogTitle>
              <DialogDescription className="text-gray-400 mt-1">
                Add new members to{' '}
                <span className="text-violet-400 font-medium gradient-text">{orgName}</span>
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {/* Role Details Toggle */}
        <button
          onClick={() => setShowRoleDetails(!showRoleDetails)}
          className="flex items-center gap-2 text-sm text-gray-400 hover:text-white transition-all duration-200 mb-4 group hover-lift px-3 py-1.5 rounded-lg hover:bg-white/5"
        >
          <Sparkles className="w-4 h-4 text-violet-400 group-hover:rotate-12 transition-transform" />
          <span>Role Permissions</span>
          <ChevronDown
            className={cn(
              'w-4 h-4 transition-transform duration-200',
              showRoleDetails && 'rotate-180',
            )}
          />
        </button>

        {/* Role Details Cards */}
        {showRoleDetails && (
          <div className="mb-6 grid grid-cols-2 gap-3">
            {ROLES.map((role, index) => (
              <Card
                key={role.id}
                className={cn(
                  'bg-[#0a0a0f] border transition-all duration-300 hover:shadow-lg hover-lift stagger-fade-in',
                  role.borderColor,
                  role.bgColor,
                  'hover:border-opacity-50',
                )}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div
                      className={cn(
                        'p-2 rounded-xl bg-gradient-to-br shadow-lg transition-transform group-hover:scale-110',
                        role.color,
                      )}
                    >
                      <role.icon className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white">{role.name}</h4>
                      <p className="text-xs text-gray-400">{role.description}</p>
                    </div>
                  </div>
                  <ul className="space-y-1.5">
                    {role.permissions.map((permission, idx) => (
                      <li
                        key={idx}
                        className="flex items-start gap-2 text-xs text-gray-300 opacity-0 animate-in fade-in slide-in-from-left duration-300"
                        style={{ animationDelay: `${index * 50 + idx * 25}ms` }}
                      >
                        <span className="text-gray-500 mt-0.5">•</span>
                        <span>{permission}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-white/5 border border-white/10 p-1 h-12 backdrop-blur-sm">
            <TabsTrigger
              value="single"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-violet-500/25 text-gray-400 transition-all duration-200 tab-indicator"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Single
            </TabsTrigger>
            <TabsTrigger
              value="multiple"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-violet-500/25 text-gray-400 transition-all duration-200 tab-indicator"
            >
              <Users className="h-4 w-4 mr-2" />
              Multiple
            </TabsTrigger>
            <TabsTrigger
              value="bulk"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-violet-500/25 text-gray-400 transition-all duration-200 tab-indicator"
            >
              <Mail className="h-4 w-4 mr-2" />
              Bulk
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="single"
            className="space-y-5 animate-in fade-in slide-in-from-bottom-2 duration-300"
          >
            <Form {...singleForm}>
              <form onSubmit={singleForm.handleSubmit(handleSingleInvite)} className="space-y-5">
                <FormField
                  control={singleForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-300 text-sm font-medium">
                        Email Address
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={isLoading}
                          className="h-12 bg-[#0a0a0f] border border-white/5 text-white placeholder:text-gray-600 focus:border-violet-500 focus:ring-1 focus:ring-violet-500/20 transition-all duration-200 focus-glow"
                        />
                      </FormControl>
                      <FormMessage className="text-red-400 text-sm" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={singleForm.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-300 text-sm font-medium">Role</FormLabel>
                      <FormControl>
                        <RoleSelector
                          value={field.value}
                          onChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage className="text-red-400 text-sm" />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    className="h-11 px-6 border-white/10 text-gray-300 hover:bg-white/5 hover:text-white hover:border-white/20 transition-all duration-200"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className={cn(
                      'h-11 px-6 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white shadow-lg shadow-violet-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover-lift',
                      isLoading && 'shimmer',
                    )}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 h-4 w-4" />
                        Send Invitation
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent
            value="multiple"
            className="space-y-5 animate-in fade-in slide-in-from-bottom-2 duration-300"
          >
            <Form {...multipleForm}>
              <form
                onSubmit={multipleForm.handleSubmit(handleMultipleInvite)}
                className="space-y-5"
              >
                <div className="space-y-3">
                  {fields.map((field, index) => (
                    <div
                      key={field.id}
                      className="flex gap-3 items-start group stagger-fade-in"
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      <div className="flex-1 grid grid-cols-2 gap-3">
                        <FormField
                          control={multipleForm.control}
                          name={`invites.${index}.email`}
                          render={({ field }) => (
                            <FormItem>
                              {index === 0 && (
                                <FormLabel className="text-gray-300 text-sm font-medium">
                                  Email Address
                                </FormLabel>
                              )}
                              <FormControl>
                                <Input
                                  type="email"
                                  placeholder="<EMAIL>"
                                  {...field}
                                  disabled={isLoading}
                                  className="h-12 bg-[#0a0a0f] border border-white/5 text-white placeholder:text-gray-600 focus:border-violet-500 focus:ring-1 focus:ring-violet-500/20 transition-all duration-200 focus-glow"
                                />
                              </FormControl>
                              <FormMessage className="text-red-400 text-sm" />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={multipleForm.control}
                          name={`invites.${index}.role`}
                          render={({ field }) => (
                            <FormItem>
                              {index === 0 && (
                                <FormLabel className="text-gray-300 text-sm font-medium">
                                  Role
                                </FormLabel>
                              )}
                              <FormControl>
                                <RoleSelector
                                  value={field.value}
                                  onChange={field.onChange}
                                  disabled={isLoading}
                                />
                              </FormControl>
                              <FormMessage className="text-red-400 text-sm" />
                            </FormItem>
                          )}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => remove(index)}
                        disabled={fields.length <= 1 || isLoading}
                        className={cn(
                          'h-12 w-12 text-gray-500 hover:text-red-400 hover:bg-red-400/10 opacity-0 group-hover:opacity-100 transition-all duration-200',
                          index === 0 && 'mt-[28px]',
                        )}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                <Button
                  type="button"
                  variant="outline"
                  onClick={() => append({ email: '', role: '' })}
                  disabled={isLoading}
                  className="w-full h-11 border-dashed border-white/10 text-gray-400 hover:text-white hover:bg-white/5 hover:border-white/20 transition-all duration-200 group"
                >
                  <Plus className="mr-2 h-4 w-4 group-hover:rotate-90 transition-transform duration-200" />
                  Add Another User
                </Button>

                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    className="h-11 px-6 border-white/10 text-gray-300 hover:bg-white/5 hover:text-white hover:border-white/20 transition-all duration-200"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className={cn(
                      'h-11 px-6 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white shadow-lg shadow-violet-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover-lift',
                      isLoading && 'shimmer',
                    )}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 h-4 w-4" />
                        Send {fields.length} Invitation{fields.length !== 1 ? 's' : ''}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent
            value="bulk"
            className="space-y-5 animate-in fade-in slide-in-from-bottom-2 duration-300"
          >
            <Form {...bulkForm}>
              <form onSubmit={bulkForm.handleSubmit(handleBulkInvite)} className="space-y-5">
                <FormField
                  control={bulkForm.control}
                  name="emails"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-300 text-sm font-medium">
                        Email Addresses
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter multiple emails (one per line or comma-separated)&#10;<EMAIL>&#10;<EMAIL>"
                          className="min-h-[120px] bg-[#0a0a0f] border border-white/5 text-white placeholder:text-gray-600 focus:border-violet-500 focus:ring-1 focus:ring-violet-500/20 transition-all duration-200 resize-none focus-glow"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormDescription className="text-gray-500 text-sm">
                        {field.value && (
                          <span className="text-violet-400 font-medium">
                            {parseBulkEmails(field.value).length} valid email(s) detected
                          </span>
                        )}
                      </FormDescription>
                      <FormMessage className="text-red-400 text-sm" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={bulkForm.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-300 text-sm font-medium">
                        Role for All Users
                      </FormLabel>
                      <FormControl>
                        <RoleSelector
                          value={field.value}
                          onChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormDescription className="text-gray-500 text-sm">
                        All users will be assigned the same role
                      </FormDescription>
                      <FormMessage className="text-red-400 text-sm" />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    className="h-11 px-6 border-white/10 text-gray-300 hover:bg-white/5 hover:text-white hover:border-white/20 transition-all duration-200"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className={cn(
                      'h-11 px-6 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white shadow-lg shadow-violet-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover-lift',
                      isLoading && 'shimmer',
                    )}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 h-4 w-4" />
                        Send Invitations
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>
        </Tabs>

        {/* Results */}
        {inviteResults.length > 0 && (
          <div className="mt-6 p-4 bg-white/5 rounded-xl border border-white/10 glass animate-in fade-in slide-in-from-bottom-2 duration-300">
            <h4 className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
              <Sparkles className="w-4 h-4 text-violet-400" />
              Results
            </h4>
            <div className="space-y-2">
              {inviteResults.map((result, index) => (
                <div
                  key={index}
                  className={cn(
                    'flex items-center gap-3 p-3 rounded-lg text-sm transition-all stagger-fade-in',
                    result.status === 'success'
                      ? 'bg-emerald-500/10 text-emerald-300 border border-emerald-500/20'
                      : 'bg-red-500/10 text-red-300 border border-red-500/20',
                  )}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  {result.status === 'success' ? (
                    <CheckCircle2 className="h-4 w-4 flex-shrink-0" />
                  ) : (
                    <AlertCircle className="h-4 w-4 flex-shrink-0" />
                  )}
                  <span className="font-medium">{result.email}</span>
                  <span className="text-xs opacity-75">{result.message}</span>
                  {result.role && (
                    <span className="ml-2 px-2 py-0.5 rounded bg-violet-700/30 text-violet-200 text-xs font-semibold">
                      Role: {result.role}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
