import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Search, Filter, ChevronDown, Download, Activity } from 'lucide-react';
import { ActionLogRow } from '@/components/team/action-log-row';
import { PaginationControls } from '@/components/team/pagination-controls';
import { TableSkeleton } from '@/components/team/table-skeleton';
import type { ActionLogRowProps } from '@/types/logs';

type ActivityLogTabProps = {
  filteredActionLogs: ActionLogRowProps[];
  actionLogsLoading: boolean;
  totalActionLogPages: number;
  pagination: {
    page: number;
    pageSize: number;
    goToPrevious: () => void;
    goToNext: (maxPages: number) => void;
    changePageSize: (size: number) => void;
  };
  actionLogFilter: string;
  setActionLogFilter: (filter: string) => void;
  actionLogSearch: string;
  setActionLogSearch: (search: string) => void;
  actions: string[];
  totalLogs: number;
};

export function ActivityLogTab({
  filteredActionLogs,
  actionLogsLoading,
  totalActionLogPages,
  pagination,
  actionLogFilter,
  setActionLogFilter,
  actionLogSearch,
  setActionLogSearch,
  actions,
  totalLogs,
}: ActivityLogTabProps) {
  return (
    <Card className="bg-[#262131] border-[#3b3b3b] shadow-xl">
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#94a3b8] w-4 h-4" />
            <Input
              placeholder="Search activity logs..."
              value={actionLogSearch}
              onChange={(e) => setActionLogSearch(e.target.value)}
              className="pl-10 bg-[#1a1a2e] border-[#3b3b3b] text-white placeholder:text-[#94a3b8] focus:ring-2 focus:ring-[#8a2be2] focus:border-transparent"
            />
          </div>

          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="bg-[#1a1a2e] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white min-w-[140px]"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {actionLogFilter}
                  <ChevronDown className="w-4 h-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-[#262131] border-[#3b3b3b]">
                <DropdownMenuItem
                  className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
                  onClick={() => setActionLogFilter('All Actions')}
                >
                  All Actions
                </DropdownMenuItem>
                {actions.map((action) => (
                  <DropdownMenuItem
                    key={action}
                    className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
                    onClick={() => setActionLogFilter(action)}
                  >
                    {action}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              variant="outline"
              size="sm"
              className="bg-[#1a1a2e] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
            >
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-[#3b3b3b] bg-[#1a1a2e]">
                {['User', 'Role', 'Action', 'Description', 'Timestamp'].map((header) => (
                  <th
                    key={header}
                    className="text-left py-4 px-6 text-[#94a3b8] font-semibold text-sm"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {actionLogsLoading ? (
                <tr>
                  <td colSpan={5} className="p-6">
                    <TableSkeleton rows={pagination.pageSize} cols={5} />
                  </td>
                </tr>
              ) : filteredActionLogs.length === 0 ? (
                <tr>
                  <td colSpan={5} className="text-center py-12">
                    <div className="text-[#94a3b8]">
                      <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No activity logs found</p>
                      <p className="text-sm">Activity will appear here as actions are performed</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredActionLogs.map((log) => (
                  <ActionLogRow key={`${log.timestamp}-${log.action}-${log.user}`} {...log} />
                ))
              )}
            </tbody>
          </table>
        </div>

        <div className="px-6 pb-4">
          <PaginationControls
            page={pagination.page}
            totalPages={totalActionLogPages}
            onPrevious={pagination.goToPrevious}
            onNext={() => pagination.goToNext(totalActionLogPages)}
            pageSize={pagination.pageSize}
            onPageSizeChange={pagination.changePageSize}
            pageSizeOptions={[10, 15, 25, 50]}
            disabled={actionLogsLoading}
            totalItems={totalLogs}
          />
        </div>
      </CardContent>
    </Card>
  );
}
