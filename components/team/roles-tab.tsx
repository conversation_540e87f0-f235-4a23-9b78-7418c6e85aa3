import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

type Role = {
  name: string;
  description: string;
  permissions: string[];
  color: string;
  members: Array<{ name: string; avatar: string | undefined; email: string }>;
  count: number;
};

type RolesTabProps = {
  rolesWithMembers: Role[];
};

export function RolesTab({ rolesWithMembers }: RolesTabProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      {rolesWithMembers.map((role) => (
        <Card
          key={role.name}
          className="bg-[#262131] border-[#3b3b3b] shadow-xl overflow-hidden group hover:shadow-2xl hover:bg-[#2a2a3e] transition-all duration-300"
        >
          <div className={`h-3 ${role.color}`} />
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-white font-bold text-lg">{role.name}</CardTitle>
              <Badge variant="secondary" className="bg-[#3b3b3b] text-white font-semibold">
                {role.count} {role.count === 1 ? 'member' : 'members'}
              </Badge>
            </div>
            <p className="text-gray-300 text-base font-medium">{role.description}</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-white font-bold mb-3 text-base">Permissions</h4>
                <div className="space-y-2">
                  {role.permissions.map((permission, idx) => (
                    <div
                      key={idx}
                      className="flex items-center text-base text-gray-200 font-medium"
                    >
                      <div className="w-2 h-2 bg-[#8a2be2] rounded-full mr-3" />
                      {permission}
                    </div>
                  ))}
                </div>
              </div>
              {role.members.length > 0 && (
                <div>
                  <h4 className="text-white font-bold mb-3 text-base">Members</h4>
                  <div className="space-y-2">
                    {role.members.slice(0, 3).map((member, idx) => (
                      <div key={idx} className="text-base text-gray-200 font-medium truncate">
                        {member.name || member.email}
                      </div>
                    ))}
                    {role.members.length > 3 && (
                      <div className="text-base text-[#8a2be2] font-semibold">
                        +{role.members.length - 3} more
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
