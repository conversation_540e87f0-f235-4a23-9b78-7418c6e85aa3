'use client';

import { useState } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

export function RemoveUserButton({
  user,
  onRemove,
  onClose,
}: {
  user: { name: string; email: string };
  onRemove: () => Promise<void>;
  onClose: () => void;
}) {
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleRemove = async () => {
    try {
      await onRemove();
      setStatus('success');
    } catch (error) {
      console.error('Failed to remove user:', error);
      setStatus('error');
    }
  };

  const closeAll = () => {
    setStatus('idle');
    onClose();
  };

  return (
    <>
      {/* Initial confirmation dialog */}
      {status === 'idle' && (
        <AlertDialog open onOpenChange={onClose}>
          <AlertDialogContent className="bg-[#121212] text-white border-gray-800 max-w-xl">
            <AlertDialogHeader>
              <AlertDialogTitle>Remove User</AlertDialogTitle>
              <AlertDialogDescription className="text-gray-300 text-center">
                Are you sure you want to remove{' '}
                <span className="font-bold text-white">{user.email}</span> from the team? <br />
                <span className="font-bold text-red-500">This action cannot be undone.</span>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel className="bg-transparent border-gray-700 hover:bg-gray-800 hover:text-white">
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction onClick={handleRemove} className="bg-red-600 hover:bg-red-700">
                Remove
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* Success dialog */}
      {status === 'success' && (
        <AlertDialog open onOpenChange={closeAll}>
          <AlertDialogContent className="bg-[#121212] text-white border-gray-800 max-w-xl">
            <AlertDialogHeader>
              <AlertDialogTitle>User Removed</AlertDialogTitle>
              <AlertDialogDescription className="text-green-400">
                {user.email} was successfully removed from the team.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction onClick={closeAll} className="bg-green-600 hover:bg-green-700">
                OK
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* Error dialog */}
      {status === 'error' && (
        <AlertDialog open onOpenChange={closeAll}>
          <AlertDialogContent className="bg-[#121212] text-white border-gray-800 max-w-xl">
            <AlertDialogHeader>
              <AlertDialogTitle>Failed to Remove User</AlertDialogTitle>
              <AlertDialogDescription className="text-red-400">
                There was an issue removing {user.email}. Please try again.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction onClick={closeAll} className="bg-red-600 hover:bg-red-700">
                Close
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );
}
