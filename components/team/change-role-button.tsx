'use client';

import { useState, useRef, useEffect } from 'react';
import { Check, ChevronDown, User, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { TeamMember } from '@/types/team';
import { getAssignableRoles, canChangeUserRole } from '@/lib/permissions';

interface Role {
  name: string;
  permissions: string[];
  description?: string;
  color?: string;
}

interface ChangeRoleButtonProps {
  user: TeamMember;
  onRoleChange: (user: TeamMember, role: string) => Promise<void>;
  onClose: () => void;
  roles: Role[];
  currentUserRole?: string | null;
}

export function ChangeRoleButton({
  user,
  onRoleChange,
  onClose,
  roles,
  currentUserRole,
}: ChangeRoleButtonProps) {
  const [selectedRole, setSelectedRole] = useState(user.role);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Get roles that the current user can assign
  const assignableRoleNames = currentUserRole ? getAssignableRoles(currentUserRole) : [];
  const availableRoles = roles.filter((role) =>
    assignableRoleNames.includes(role.name.toLowerCase()),
  );

  // Check if current user can change this specific user's role
  const canChangeThisUserRole =
    currentUserRole && canChangeUserRole(currentUserRole, user.role, selectedRole).canChange;

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    }

    if (dropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen]);

  const handleSave = () => {
    if (!currentUserRole) {
      setErrorMessage('Unable to determine your role permissions');
      return;
    }

    const permissionCheck = canChangeUserRole(currentUserRole, user.role, selectedRole);
    if (!permissionCheck.canChange) {
      setErrorMessage(permissionCheck.reason || 'You do not have permission to make this change');
      return;
    }

    setErrorMessage(null);
    setConfirmOpen(true);
  };

  const handleConfirm = async () => {
    setIsLoading(true);
    try {
      await onRoleChange(user, selectedRole);
      setConfirmOpen(false);
      onClose();
    } catch (err: any) {
      setErrorMessage(err.message || 'Failed to update role. Please try again.');
      setConfirmOpen(false);
    } finally {
      setIsLoading(false);
    }
  };

  const selectedRoleData = roles.find((role) => role.name === selectedRole);

  return (
    <>
      <Dialog open onOpenChange={onClose}>
        <DialogContent className="sm:max-w-2xl bg-[#262131] border-[#3b3b3b] shadow-2xl">
          <DialogHeader className="pb-6">
            <DialogTitle className="text-2xl font-bold text-white flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg">
                <Shield className="w-5 h-5 text-white" />
              </div>
              Change User Role
            </DialogTitle>
            <p className="text-[#94a3b8] text-sm mt-2">
              Update the role and permissions for this team member
            </p>
          </DialogHeader>

          <div className="space-y-8 py-4">
            {/* User Info Section */}
            <div className="bg-[#1a1a2e] rounded-xl p-6 border border-[#3b3b3b]">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">{user.name || 'Team Member'}</h3>
                  <p className="text-[#94a3b8] text-sm">{user.email}</p>
                </div>
              </div>
            </div>

            {/* Role Selection */}
            <div className="space-y-4">
              <label className="text-sm font-medium text-[#94a3b8]">Select New Role</label>
              {availableRoles.length === 0 ? (
                <div className="p-4 bg-[#1a1a2e] border-2 border-[#3b3b3b] rounded-xl">
                  <p className="text-[#94a3b8] text-center">
                    You don't have permission to change roles for this user
                  </p>
                </div>
              ) : (
                <div className="relative" ref={dropdownRef}>
                  <button
                    ref={buttonRef}
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    className="flex items-center justify-between w-full p-4 bg-[#1a1a2e] border-2 border-[#3b3b3b] rounded-xl hover:border-purple-400 focus:border-purple-500 focus:ring-4 focus:ring-purple-500/20 transition-all duration-200"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"></div>
                      <span className="font-medium text-white">{selectedRole}</span>
                    </div>
                    <ChevronDown
                      className={`h-5 w-5 text-[#94a3b8] transition-transform ${dropdownOpen ? 'rotate-180' : ''}`}
                    />
                  </button>

                  {dropdownOpen && (
                    <div className="absolute z-50 mt-2 w-full bg-[#262131] border border-[#3b3b3b] rounded-xl shadow-2xl overflow-hidden">
                      <div className="max-h-80 overflow-y-auto">
                        {availableRoles
                          .filter((role) => role.name !== 'Owner')
                          .map((role) => (
                            <div
                              key={role.name}
                              className={`p-4 cursor-pointer transition-all duration-150 hover:bg-[#3b3b3b] border-b border-[#3b3b3b] last:border-b-0 ${
                                role.name === selectedRole
                                  ? 'bg-[#3b3b3b] border-l-4 border-l-purple-500'
                                  : ''
                              }`}
                              onClick={() => {
                                setSelectedRole(role.name);
                                setDropdownOpen(false);
                              }}
                            >
                              <div className="flex items-start gap-3">
                                <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full mt-1 flex-shrink-0"></div>
                                <div className="flex-1">
                                  <div className="font-semibold text-white mb-1">{role.name}</div>
                                  <div className="text-sm text-[#94a3b8] mb-2">
                                    {role.description}
                                  </div>
                                  <div className="space-y-1">
                                    {role.permissions.slice(0, 3).map((permission, index) => (
                                      <div
                                        key={index}
                                        className="flex items-center text-xs text-[#94a3b8]"
                                      >
                                        <span className="mr-2">•</span>
                                        <span>{permission}</span>
                                      </div>
                                    ))}
                                    {role.permissions.length > 3 && (
                                      <div className="text-xs text-purple-400 font-medium">
                                        +{role.permissions.length - 3} more permissions
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Selected Role Preview */}
              {selectedRoleData && (
                <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-xl p-6 border border-purple-500/30">
                  <h4 className="font-semibold text-white mb-3 flex items-center gap-2">
                    <Shield className="w-4 h-4 text-purple-400" />
                    {selectedRoleData.name} Permissions
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    {selectedRoleData.permissions.map((permission, index) => (
                      <div key={index} className="flex items-center text-sm text-[#94a3b8]">
                        <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-3"></div>
                        <span>{permission}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {errorMessage && (
            <div className="bg-red-500/10 border border-red-500/30 text-red-400 text-sm rounded-xl p-4 flex items-start gap-3">
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs">!</span>
              </div>
              <span>{errorMessage}</span>
            </div>
          )}

          <DialogFooter className="pt-6 border-t border-[#3b3b3b] justify-between">
            <Button
              variant="outline"
              onClick={onClose}
              className="border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg"
              disabled={
                selectedRole === user.role ||
                isLoading ||
                !canChangeThisUserRole ||
                availableRoles.length === 0
              }
            >
              <Check className="mr-2 h-4 w-4" />
              {isLoading ? 'Updating...' : 'Update Role'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <AlertDialogContent className="bg-[#262131] border-[#3b3b3b] shadow-2xl max-w-xl">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl font-bold text-white flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-orange-400 to-red-500 rounded-lg">
                <Shield className="w-5 h-5 text-white" />
              </div>
              Confirm Role Change
            </AlertDialogTitle>
            <AlertDialogDescription className="sr-only">
              Confirm role change for {user.email}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4">
            <div className="bg-amber-500/10 border border-amber-500/30 rounded-lg p-4">
              <div className="font-medium text-amber-400 mb-2">
                You are about to change the role for:
              </div>
              <div className="bg-[#1a1a2e] rounded-lg p-3 border border-[#3b3b3b]">
                <div className="font-semibold text-white">{user.email}</div>
                <div className="text-sm text-[#94a3b8]">
                  From <span className="font-medium text-red-400">{user.role}</span> to{' '}
                  <span className="font-medium text-green-400">{selectedRole}</span>
                </div>
              </div>
            </div>
            <div className="text-sm text-[#94a3b8]">
              This will immediately update their permissions and access level.
            </div>
          </div>

          <AlertDialogFooter className="gap-3">
            <AlertDialogCancel
              className="border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
              disabled={isLoading}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirm}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
              disabled={isLoading}
            >
              {isLoading ? 'Updating...' : 'Confirm Change'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
