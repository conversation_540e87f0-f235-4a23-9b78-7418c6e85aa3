import { Button } from '@/components/ui/button';

type PaginationControlsProps = {
  page: number;
  totalPages: number;
  onPrevious: () => void;
  onNext: () => void;
  pageSize: number;
  onPageSizeChange: (size: number) => void;
  pageSizeOptions?: number[];
  disabled?: boolean;
  totalItems?: number;
};

export function PaginationControls({
  page,
  totalPages,
  onPrevious,
  onNext,
  pageSize,
  onPageSizeChange,
  pageSizeOptions = [5, 10, 25, 50],
  disabled = false,
  totalItems = 0,
}: PaginationControlsProps) {
  const startItem = page * pageSize + 1;
  const endItem = Math.min((page + 1) * pageSize, totalItems);

  return (
    <div className="flex items-center justify-between mt-6 flex-wrap gap-4">
      <div className="flex items-center space-x-3">
        <Button
          variant="outline"
          size="sm"
          className="bg-[#262131] border-[#3b3b3b] text-white hover:bg-[#3b3b3b] disabled:opacity-50 font-semibold"
          onClick={onPrevious}
          disabled={disabled || page === 0}
        >
          Previous
        </Button>
        <span className="text-gray-200 text-base font-medium px-3">
          {totalItems > 0 ? `${startItem}-${endItem} of ${totalItems}` : 'No items'}
        </span>
        <Button
          variant="outline"
          size="sm"
          className="bg-[#262131] border-[#3b3b3b] text-white hover:bg-[#3b3b3b] disabled:opacity-50 font-semibold"
          onClick={onNext}
          disabled={disabled || page >= totalPages - 1}
        >
          Next
        </Button>
      </div>
      <div className="flex items-center space-x-3">
        <span className="text-gray-200 text-base font-medium">Show:</span>
        <select
          value={pageSize}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
          className="px-4 py-2 rounded bg-[#262131] border border-[#3b3b3b] text-white text-base font-medium hover:bg-[#3b3b3b] focus:ring-2 focus:ring-[#8a2be2] focus:outline-none"
        >
          {pageSizeOptions.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
