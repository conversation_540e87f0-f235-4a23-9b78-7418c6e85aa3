import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Search, Filter, ChevronDown, Users } from 'lucide-react';
import { TeamMemberRow } from '@/components/team/team-member-row';
import { PaginationControls } from '@/components/team/pagination-controls';
import { TableSkeleton } from '@/components/team/table-skeleton';
import type { TeamMember } from '@/types/team';

type TeamMembersTabProps = {
  filteredUsers: TeamMember[];
  usersLoading: boolean;
  totalUserPages: number;
  pagination: {
    page: number;
    pageSize: number;
    goToPrevious: () => void;
    goToNext: (maxPages: number) => void;
    changePageSize: (size: number) => void;
  };
  roleFilter: string;
  setRoleFilter: (role: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  roles: Array<{ name: string }>;
  totalUsers: number;
  currentUserRole?: string | null;
  onChangeRole: (member: TeamMember) => void;
  onRemove: (member: TeamMember) => void;
};

export function TeamMembersTab({
  filteredUsers,
  usersLoading,
  totalUserPages,
  pagination,
  roleFilter,
  setRoleFilter,
  searchTerm,
  setSearchTerm,
  roles,
  totalUsers,
  currentUserRole,
  onChangeRole,
  onRemove,
}: TeamMembersTabProps) {
  return (
    <Card className="bg-[#262131] border-[#3b3b3b] shadow-xl">
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Search members by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-11 bg-[#1a1a2e] border-[#3b3b3b] text-white placeholder:text-gray-400 focus:ring-2 focus:ring-[#8a2be2] focus:border-transparent text-base font-medium"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="bg-[#1a1a2e] border-[#3b3b3b] text-gray-300 hover:bg-[#3b3b3b] hover:text-white min-w-[140px] font-semibold"
              >
                <Filter className="w-5 h-5 mr-2" />
                {roleFilter}
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-[#262131] border-[#3b3b3b]">
              <DropdownMenuItem
                className="text-gray-300 hover:text-white hover:bg-[#3b3b3b] font-medium"
                onClick={() => setRoleFilter('All Roles')}
              >
                All Roles
              </DropdownMenuItem>
              {roles.map((role) => (
                <DropdownMenuItem
                  key={role.name}
                  className="text-gray-300 hover:text-white hover:bg-[#3b3b3b] font-medium"
                  onClick={() => setRoleFilter(role.name)}
                >
                  {role.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-[#3b3b3b] bg-[#1a1a2e]">
                {['Member', 'Role', 'Credits', 'Status', 'Last Active', 'Actions'].map((header) => (
                  <th
                    key={header}
                    className="text-left py-5 px-6 text-gray-200 font-bold text-base"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {usersLoading ? (
                <tr>
                  <td colSpan={6} className="p-6">
                    <TableSkeleton rows={pagination.pageSize} />
                  </td>
                </tr>
              ) : filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center py-12">
                    <div className="text-gray-300">
                      <Users className="w-16 h-16 mx-auto mb-6 opacity-60" />
                      <p className="text-xl font-bold mb-3">No team members found</p>
                      <p className="text-base font-medium">
                        Try adjusting your search or filter criteria
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredUsers.map((member) => (
                  <TeamMemberRow
                    key={member.email}
                    user={{
                      ...member,
                      userCredits: member.userCredits ?? 0,
                      lastActive: member.lastActive ?? '',
                    }}
                    currentUserRole={currentUserRole}
                    onChangeRole={() => onChangeRole(member)}
                    onRemove={() => onRemove(member)}
                  />
                ))
              )}
            </tbody>
          </table>
        </div>

        <div className="px-6 pb-4">
          <PaginationControls
            page={pagination.page}
            totalPages={totalUserPages}
            onPrevious={pagination.goToPrevious}
            onNext={() => pagination.goToNext(totalUserPages)}
            pageSize={pagination.pageSize}
            onPageSizeChange={pagination.changePageSize}
            disabled={usersLoading}
            totalItems={totalUsers}
          />
        </div>
      </CardContent>
    </Card>
  );
}
