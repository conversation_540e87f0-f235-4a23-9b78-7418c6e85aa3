'use client';

import { MoreHorizontal } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { canChangeUserRole, canRemoveUser } from '@/lib/permissions';

interface TeamMemberRowProps {
  user: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
    status: 'Active' | 'Deactivated' | 'Invited';
    userCredits: number;
    lastActive: string;
  };
  currentUserRole?: string | null; // Role of the user viewing this row
  onChangeRole?: () => void;
  onRemove?: () => void;
}

export function TeamMemberRow({
  user,
  currentUserRole,
  onChangeRole,
  onRemove,
}: TeamMemberRowProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Active':
        return (
          <Badge className="bg-[#22c55e] text-white hover:bg-[#22c55e]/90 font-semibold px-3 py-1">
            Active
          </Badge>
        );
      case 'Deactivated':
        return (
          <Badge className="bg-[#ef4444] text-white hover:bg-[#ef4444]/90 font-semibold px-3 py-1">
            Deactivated
          </Badge>
        );
      case 'Invited':
        return (
          <Badge className="bg-[#3b82f6] text-white hover:bg-[#3b82f6]/90 font-semibold px-3 py-1">
            Invited
          </Badge>
        );
      default:
        return <Badge className="bg-[#94a3b8] text-white font-semibold px-3 py-1">Unknown</Badge>;
    }
  };

  const getInitials = (name: string) => {
    if (!name || name.trim() === '') {
      return '??';
    }
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2); // Limit to 2 characters
  };

  const formatLastActive = (date: string) => {
    if (!date) return 'Never';
    try {
      const d = new Date(date);
      if (isNaN(d.getTime())) return 'Never';
      return d.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Never';
    }
  };

  // Check permissions for role management
  const canChangeRole = currentUserRole
    ? canChangeUserRole(currentUserRole, user.role, 'member').canChange
    : false;

  const canRemove = currentUserRole ? canRemoveUser(currentUserRole, user.role).canRemove : false;

  // Don't show actions if user has no permissions
  const hasAnyPermission = canChangeRole || canRemove;

  return (
    <tr className="border-b border-[#3b3b3b] hover:bg-[#262131]/70 transition-colors">
      <td className="py-5 px-6">
        <div className="flex items-center space-x-4">
          <Avatar className="w-10 h-10">
            <AvatarImage src={user.avatar || '/placeholder.svg'} />
            <AvatarFallback className="bg-[#8a2be2] text-white text-sm font-semibold">
              {getInitials(user.name || user.email)}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="text-white font-semibold text-base">{user.name || user.email}</p>
            <p className="text-gray-300 text-sm font-medium">{user.email}</p>
          </div>
        </div>
      </td>
      <td className="py-5 px-6">
        <span className="text-gray-200 text-base font-medium capitalize">{user.role}</span>
      </td>
      <td className="py-5 px-6">
        <span className="text-gray-200 text-base font-medium">{user.userCredits || 0}</span>
      </td>
      <td className="py-5 px-6">{getStatusBadge(user.status)}</td>
      <td className="py-5 px-6">
        <span className="text-gray-200 text-base font-medium">
          {formatLastActive(user.lastActive)}
        </span>
      </td>
      <td className="py-5 px-6">
        {hasAnyPermission ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white p-2">
                <MoreHorizontal className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-[#262131] border-[#3b3b3b]">
              {canChangeRole && (
                <DropdownMenuItem
                  className="text-gray-300 hover:text-white hover:bg-[#3b3b3b] font-medium"
                  onClick={onChangeRole}
                >
                  Change Role
                </DropdownMenuItem>
              )}

              {canRemove && (
                <DropdownMenuItem
                  className="text-[#ef4444] hover:text-[#ef4444] hover:bg-[#3b3b3b] font-medium"
                  onClick={onRemove}
                >
                  Remove Member
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <span className="text-gray-500 text-sm">No actions</span>
        )}
      </td>
    </tr>
  );
}
