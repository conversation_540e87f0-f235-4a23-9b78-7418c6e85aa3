'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { CheckCircle, Mail, AlertCircle, ExternalLink, RefreshCw } from 'lucide-react';
import Link from 'next/link';

export function VerifyForm() {
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get email from URL parameters
  const emailParam = searchParams.get('email');
  const email = emailParam ? decodeURIComponent(emailParam) : '';

  // Handle verification link callback parameters
  useEffect(() => {
    const confirmationCode = searchParams.get('confirmation_code');
    const username = searchParams.get('username');

    if (confirmationCode && username) {
      // This is a verification link callback
      handleVerificationLink(username, confirmationCode);
    }
  }, [searchParams]);

  const handleVerificationLink = async (username: string, confirmationCode: string) => {
    try {
      console.log('🔗 [VERIFY] Processing verification link...');

      const response = await fetch('/api/auth/verify-link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, confirmationCode }),
      });

      const result = await response.json();

             if (result.success) {
         console.log('✅ [VERIFY] Email verification successful');
         router.push('/signin?message=Email verified successfully! You can now sign in.');
       } else {
        console.error('❌ [VERIFY] Verification failed:', result.error);
        router.push(
          `/verify?email=${encodeURIComponent(username)}&error=${encodeURIComponent(result.error)}`,
        );
      }
    } catch (error) {
      console.error('❌ [VERIFY] Verification error:', error);
      router.push(`/verify?email=${encodeURIComponent(username)}&error=Verification failed`);
    }
  };

  const handleResendLink = async () => {
    if (!email) return;

    setIsResending(true);
    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (result.success) {
        setResendSuccess(true);
        setTimeout(() => setResendSuccess(false), 5000);
      }
    } catch (error) {
      console.error('Failed to resend verification link:', error);
    } finally {
      setIsResending(false);
    }
  };

  const verificationError = searchParams.get('error');

  return (
    <div className="w-full bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] rounded-3xl shadow-2xl border border-purple-800/20 backdrop-blur-sm">
      <div className="p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 mb-6 mx-auto shadow-lg shadow-purple-500/25">
            <Mail className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Check Your Email</h1>
          <p className="text-slate-400 text-sm">Click the verification link sent to your email</p>
        </div>

        {/* Error Alert */}
        {verificationError && (
          <div className="flex items-start gap-3 p-4 rounded-xl bg-red-500/10 border border-red-500/20 backdrop-blur-sm mb-6">
            <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
            <p className="text-red-400 text-sm">{verificationError}</p>
          </div>
        )}

        {/* Success message for resend */}
        {resendSuccess && (
          <div className="flex items-start gap-3 p-4 rounded-xl bg-green-500/10 border border-green-500/20 backdrop-blur-sm mb-6">
            <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
            <p className="text-green-400 text-sm">Verification link sent! Check your email.</p>
          </div>
        )}

        {/* Email Address */}
        {email && (
          <div className="space-y-2 mb-8">
            <label className="text-sm font-medium text-slate-300 block">Email Address</label>
            <div className="relative group">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500" />
              <input
                type="email"
                value={email}
                disabled
                className="w-full pl-10 pr-4 py-3.5 rounded-xl border border-slate-600/50 bg-slate-800/30 text-slate-400 cursor-not-allowed"
              />
            </div>
            <p className="text-xs text-slate-500 flex items-center gap-1">
              <CheckCircle className="w-3 h-3" />
              Verification link sent to this email address
            </p>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-slate-800/30 rounded-xl border border-slate-700/50 p-6 mb-6">
          <div className="flex items-start gap-3">
            <ExternalLink className="w-5 h-5 text-purple-400 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-sm font-semibold text-white mb-2">Next Steps</h3>
              <ol className="text-sm text-slate-400 space-y-1 list-decimal list-inside">
                <li>Check your email inbox (and spam folder)</li>
                <li>Look for an email from qBraid</li>
                <li>Click the "Verify Email" link in the email</li>
                <li>You'll be automatically redirected to sign in</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Resend Link */}
        {email && (
          <div className="text-center mb-6">
            <button
              onClick={handleResendLink}
              disabled={isResending}
              className="inline-flex items-center gap-2 px-4 py-2 text-sm text-purple-400 hover:text-purple-300 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isResending ? 'animate-spin' : ''}`} />
              {isResending ? 'Sending...' : 'Resend verification link'}
            </button>
          </div>
        )}

        {/* Help Text */}
        <div className="mt-8 p-4 bg-slate-800/30 rounded-xl border border-slate-700/50">
          <p className="text-sm text-slate-400 text-center">
            Still having trouble?{' '}
            <Link
              href="/signup"
              className="text-purple-400 hover:text-purple-300 font-semibold hover:underline transition-colors"
            >
              Try signing up again
            </Link>
          </p>
        </div>

        {/* Back to Sign In */}
        <p className="text-center text-sm text-slate-400 mt-6">
          Remember your password?{' '}
          <Link
            href="/signin"
            className="text-purple-400 hover:text-purple-300 font-semibold hover:underline transition-colors"
          >
            Sign in instead
          </Link>
        </p>
        <p className="text-center text-sm text-slate-400 mt-10">
          &copy; 2025 qBraid Co. All rights reserved.
        </p>
      </div>
    </div>
  );
}
