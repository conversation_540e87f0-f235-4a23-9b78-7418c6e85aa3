'use client';

import React from 'react';
import { usePermissions } from '@/hooks/use-permissions';
import { LoaderThree } from '@/components/ui/loader';
import { Permission } from '@/types/auth';

interface RoleLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showBackgroundUpdates?: boolean; // New prop to optionally show background loading
}

/**
 * Component that ensures roles are loaded before showing children
 * Only shows full loading screen on initial load - subsequent navigations use cached data
 */
export function RoleLoader({ children, fallback, showBackgroundUpdates = false }: RoleLoaderProps) {
  console.log(`🔍 [ROLE-LOADER] ========== COMPONENT RENDERED ==========`);
  console.log(`🔍 [ROLE-LOADER] Timestamp: ${new Date().toISOString()}`);
  console.log(`🔍 [ROLE-LOADER] Props:`, {
    hasFallback: !!fallback,
    showBackgroundUpdates: showBackgroundUpdates,
  });

  const { loading, error, roles, permissions, queryData, isRefreshing } = usePermissions();

  console.log(`📊 [ROLE-LOADER] usePermissions hook data:`, {
    loading: loading,
    isRefreshing: isRefreshing,
    hasError: !!error,
    errorMessage: error || null,
    rolesCount: roles.length,
    roles: roles,
    permissionsCount: permissions.length,
    permissions: permissions,
    hasQueryData: !!queryData,
    queryDataSource: queryData?.source || 'unknown',
    queryDataCached: queryData?.cached || false,
  });

  // Only show full loading screen if we have no data at all (initial load)
  const isInitialLoad = loading && !queryData;

  console.log(`📋 [ROLE-LOADER] Loading state analysis:`, {
    loading: loading,
    hasQueryData: !!queryData,
    isInitialLoad: isInitialLoad,
    isRefreshing: isRefreshing,
    shouldShowLoading: isInitialLoad,
    shouldShowError: error && !roles.length && !queryData,
  });

  // Show custom fallback if provided (only for initial load)
  if (fallback && isInitialLoad) {
    console.log(`🎭 [ROLE-LOADER] SHOWING CUSTOM FALLBACK - Initial load with custom fallback`);
    return <>{fallback}</>;
  }

  // Show loading state ONLY on initial load when we have no cached data
  if (isInitialLoad) {
    console.log(`⏳ [ROLE-LOADER] SHOWING LOADING STATE - Initial load without cached data`);
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-center space-y-6">
          {/* Simple animated logo */}
          <div className="flex justify-center">
            <LoaderThree />
          </div>

          {/* Clean text */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-white">Loading workspace</h2>
            <p className="text-gray-400 text-sm">Fetching your permissions</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state only if we have no cached data and an error occurred
  if (error && !roles.length && !queryData) {
    console.log(`❌ [ROLE-LOADER] SHOWING ERROR STATE - Error with no cached data:`, {
      error: error,
      rolesCount: roles.length,
      hasQueryData: !!queryData,
    });

    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-center space-y-6 max-w-sm">
          {/* Simple error icon */}
          <div className="flex justify-center">
            <div className="w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center">
              <svg
                className="w-6 h-6 text-red-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>

          {/* Error message */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-white">Connection failed</h2>
            <p className="text-gray-400 text-sm">Unable to load your account data</p>
          </div>

          {/* Retry button */}
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-colors"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  console.log(`✅ [ROLE-LOADER] RENDERING CHILDREN - Using available data (cached or fresh)`);
  console.log(`✅ [ROLE-LOADER] Final render state:`, {
    rolesCount: roles.length,
    permissionsCount: permissions.length,
    isRefreshing: isRefreshing,
    showBackgroundIndicator: showBackgroundUpdates && (loading || isRefreshing) && queryData,
  });

  // Always render children if we have any data (cached or fresh)
  // This allows instant navigation between pages using cached permission data
  return (
    <div className="relative">
      {/* Subtle background loading indicator */}
      {showBackgroundUpdates && (loading || isRefreshing) && queryData && (
        <div className="fixed top-0 left-0 right-0 z-50 h-1 bg-blue-500/20">
          <div className="h-full bg-blue-500 animate-pulse" style={{ width: '30%' }} />
        </div>
      )}
      {children}
    </div>
  );
}

/**
 * Lightweight permission checker for selective use
 * Use this instead of RoleLoader when you only need to protect specific components
 */
interface PermissionWrapperProps {
  children: React.ReactNode;
  requiredPermissions?: Permission[];
  requiredRoles?: string[];
  fallback?: React.ReactNode;
  showSkeleton?: boolean;
}

export function PermissionWrapper({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  fallback,
  showSkeleton = true,
}: PermissionWrapperProps) {
  const { loading, roles, permissions, queryData } = usePermissions();

  // If we have cached data, proceed immediately without loading state
  if (queryData) {
    const hasRequiredPermissions =
      requiredPermissions.length === 0 || requiredPermissions.some((p) => permissions.includes(p));
    const hasRequiredRoles =
      requiredRoles.length === 0 || requiredRoles.some((r) => roles.includes(r));

    if (hasRequiredPermissions && hasRequiredRoles) {
      return <>{children}</>;
    }

    return (
      fallback || (
        <div className="text-center py-4 text-gray-500">
          <p>You don't have permission to view this content.</p>
        </div>
      )
    );
  }

  // Only show loading for initial fetch
  if (loading && showSkeleton) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 bg-gray-300 rounded w-3/4"></div>
        <div className="h-4 bg-gray-300 rounded w-1/2"></div>
      </div>
    );
  }

  // Default fallback during loading
  return fallback || <div>Loading...</div>;
}

/**
 * Lightweight direct role checker - bypasses complex caching
 * Use this for simple role checks without full RBAC complexity
 */
interface DirectRoleCheckProps {
  children: React.ReactNode;
  requiredRole?: string;
  orgId?: string;
  fallback?: React.ReactNode;
}

export function DirectRoleCheck({ children, requiredRole, orgId, fallback }: DirectRoleCheckProps) {
  const [userRole, setUserRole] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    if (orgId) {
      // Direct API call - bypasses complex system
      fetch(`/api/user/roles?orgId=${orgId}`)
        .then((res) => res.json())
        .then((data) => {
          setUserRole(data.currentUserRole || null);
          setLoading(false);
        })
        .catch(() => {
          setUserRole(null);
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [orgId]);

  if (loading) {
    return <div className="animate-pulse h-4 bg-gray-200 rounded w-1/2" />;
  }

  if (requiredRole && userRole !== requiredRole) {
    return fallback || <div className="text-gray-500">Access denied</div>;
  }

  return <>{children}</>;
}
