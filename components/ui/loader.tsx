'use client';
import { motion } from 'framer-motion';
import React from 'react';

// Minimal bouncing dots - 3 dots with simple bounce
export const LoaderOne = () => {
  return (
    <div className="flex items-center gap-1">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-2 h-2 rounded-full bg-purple-400"
          animate={{ y: [0, -8, 0] }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: i * 0.1,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );
};

// Minimal sliding dots - simple horizontal movement
export const LoaderTwo = () => {
  return (
    <div className="flex items-center space-x-1">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-2 h-2 rounded-full bg-blue-400"
          animate={{ x: [0, 12, 0] }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );
};

// Minimal spinning circle - single element
export const LoaderThree = () => {
  return (
    <motion.div
      className="w-8 h-8 border-2 border-purple-400 border-t-transparent rounded-full"
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: 'linear',
      }}
    />
  );
};

// Minimal pulse - simple scale animation
export const LoaderFour = ({ text = 'Loading...' }: { text?: string }) => {
  return (
    <motion.div
      className="text-sm font-medium text-gray-400"
      animate={{ opacity: [0.5, 1, 0.5] }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    >
      {text}
    </motion.div>
  );
};
