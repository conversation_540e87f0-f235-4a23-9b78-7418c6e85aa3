import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'motion/react';
import { DeviceJobsTab } from '@/components/devices/device-jobs-tab';
import { CalibrationMetrics } from '@/components/devices/calibration-metrics';
import { Loader2 } from 'lucide-react';
import { QuantumTopology } from '@/components/devices/quantum-topology';
import type { DeviceCardProps } from '@/types/device';
import { DeviceDetailsTab } from '@/components/devices/device-details-tab';

interface DevicesSectionProps {
  device: DeviceCardProps;
}

// Reusable animated wrapper for tab content
const AnimatedPanel = ({ id, children }: { id: string; children: React.ReactNode }) => (
  <AnimatePresence mode="wait">
    <motion.div
      key={id}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.15 }}
    >
      {children}
    </motion.div>
  </AnimatePresence>
);

export function DevicesSection({ device }: DevicesSectionProps) {
  // not using static infoRows anymore since we switched to DataTable columns

  return (
    <Tabs defaultValue="details" className="w-full">
      {/* Tab Bar */}
      <div className="overflow-x-auto mb-6">
        <TabsList className="bg-transparent pt-1 pb-0 rounded-lg w-max rounded-b-none">
          <TabsTrigger
            value="details"
            className="rounded-md rounded-b-none px-4 py-2 border-b-2 text-sm data-[state=active]:bg-[#32164b] data-[state=active]:border-x-2 data-[state=active]:border-t-2 data-[state=active]:border-b-0 data-[state=active]:text-white "
          >
            Device Details
          </TabsTrigger>
          <TabsTrigger
            value="jobs"
            className="rounded-md rounded-b-none px-4 py-2 border-b-2 text-sm data-[state=active]:bg-[#32164b] data-[state=active]:border-x-2 data-[state=active]:border-t-2 data-[state=active]:border-b-0 data-[state=active]:text-white"
          >
            Jobs Overview
          </TabsTrigger>
          <TabsTrigger
            value="topology"
            className="rounded-md rounded-b-none px-4 py-2 border-b-2 text-sm data-[state=active]:bg-[#32164b] data-[state=active]:border-x-2 data-[state=active]:border-t-2 data-[state=active]:border-b-0 data-[state=active]:text-white"
          >
            Topology & Calibration
          </TabsTrigger>
        </TabsList>
      </div>

      {/* Details Tab */}
      <TabsContent value="details" className="mt-0">
        <AnimatedPanel id={`details-${device.qbraid_id}`}>
          <div className="overflow-x-auto">
            <DeviceDetailsTab device={device} />
          </div>
        </AnimatedPanel>
      </TabsContent>

      {/* Jobs Tab */}
      <TabsContent value="jobs" className="mt-0">
        <AnimatedPanel id={`jobs-${device.qbraid_id}`}>
          <DeviceJobsTab provider={device.provider} deviceId={device.qbraid_id} />
        </AnimatedPanel>
      </TabsContent>

      {/* Topology & Calibration Tab */}
      <TabsContent value="topology" className="mt-0 space-y-8">
        <AnimatedPanel id={`topology-${device.qbraid_id}`}>
          <div className="space-y-8">
            <QuantumTopology device={device as any} />
            <CalibrationMetrics
              metrics={[
                {
                  name: 'Readout Assignment Error',
                  median: '1.587e-2',
                  min: '6.615e-3',
                  max: '2.222e-1',
                  color: '#3b82f6',
                  value: 40,
                },
                {
                  name: 'ECR Error',
                  median: '6.560e-3',
                  min: '3.206e-3',
                  max: '2.481e-1',
                  color: '#3b82f6',
                  value: 30,
                },
                { name: 'SX Error', median: '2.219e-4', color: '#3b82f6', value: 75 },
                { name: 'T1 Relaxation Time', median: '280.82 μs', color: '#8b5cf6', value: 60 },
                { name: 'T2 Coherence Time', median: '214.87 μs', color: '#8b5cf6', value: 50 },
              ]}
            />
          </div>
        </AnimatedPanel>
      </TabsContent>
    </Tabs>
  );
}
