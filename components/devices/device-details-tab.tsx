import { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Package, DollarSign, ChevronDown, CheckCircle2Icon, LoaderIcon } from 'lucide-react';

import { DataTable } from '@/components/ui/data-table';
import { Badge } from '@/components/ui/badge';
import type { DeviceCardProps } from '@/types/device';

export function DeviceDetailsTab({ device }: { device: DeviceCardProps }) {
  /* -------------------------------- Columns ------------------------------- */
  const systemColumns: ColumnDef<DeviceCardProps & { id: string }>[] = [
    { accessorKey: 'paradigm', header: 'Paradigm' },
    { accessorKey: 'provider', header: 'Vendor' },
    { accessorKey: 'type', header: 'Type' },
    {
      accessorKey: 'architecture',
      header: 'Architecture',
      cell: ({ row }) => row.original.architecture || row.original.processorType || 'N/A',
    },
    { accessorKey: 'numberQubits', header: 'Qubits' },
    { accessorKey: 'runPackage', header: 'Run Package' },
    {
      accessorKey: 'noiseModels',
      header: 'Noise Models',
      cell: ({ row }) => row.original.noiseModels?.join(', ') || 'N/A',
    },
  ];

  const pricingColumns: ColumnDef<DeviceCardProps & { id: string }>[] = [
    {
      accessorKey: 'pricing.perMinute',
      header: 'Cost / Minute',
      cell: ({ row }) => {
        const v = row.original.pricing?.perMinute;
        return v !== null && v !== undefined && v !== '' ? `$${v}` : 'Free';
      },
    },
    {
      accessorKey: 'pricing.perTask',
      header: 'Cost / Task',
      cell: ({ row }) => {
        const v = row.original.pricing?.perTask;
        return v !== null && v !== undefined && v !== '' ? `$${v}` : 'Free';
      },
    },
    {
      accessorKey: 'pricing.perShot',
      header: 'Cost / Shot',
      cell: ({ row }) => {
        const v = row.original.pricing?.perShot;
        return v !== null && v !== undefined && v !== '' ? `$${v}` : 'Free';
      },
    },
    { accessorKey: 'pendingJobs', header: 'Pending Jobs' },
    {
      accessorKey: 'status',
      header: 'Queue Status',
      cell: ({ row }) => (
        <Badge
          variant="outline"
          className="flex gap-1 px-2 text-muted-foreground [&_svg]:size-3 w-fit"
        >
          {['online', 'active'].includes((row.original.status || '').toLowerCase()) ? (
            <CheckCircle2Icon className="text-green-500" />
          ) : (
            <LoaderIcon className="animate-spin" />
          )}
          {row.original.status || 'unknown'}
        </Badge>
      ),
    },
  ];

  /* ----------------------------- Collapse State ---------------------------- */
  const [showSystem, setShowSystem] = useState(true);
  const [showPricing, setShowPricing] = useState(true);

  const tableData = [{ id: device.qbraid_id, ...device }];

  return (
    <div className="space-y-6">
      {/* System Information */}
      <div>
        <button
          onClick={() => setShowSystem((s) => !s)}
          className="flex items-center gap-2 text-white"
        >
          <Package className="size-4 text-[#8a2be2]" />
          <span className="font-semibold">System Information</span>
          <ChevronDown
            className={`size-4 transition-transform ${showSystem ? 'rotate-180' : ''}`}
          />
        </button>
        {showSystem && (
          <div className="mt-2">
            <DataTable data={tableData} columnsOverride={systemColumns} hideToolbar />
          </div>
        )}
      </div>

      {/* Pricing & Operations */}
      <div>
        <button
          onClick={() => setShowPricing((p) => !p)}
          className="flex items-center gap-2 text-white"
        >
          <DollarSign className="size-4 text-[#8a2be2]" />
          <span className="font-semibold">Pricing & Operations</span>
          <ChevronDown
            className={`size-4 transition-transform ${showPricing ? 'rotate-180' : ''}`}
          />
        </button>
        {showPricing && (
          <div className="mt-2">
            <DataTable data={tableData} columnsOverride={pricingColumns} hideToolbar />
          </div>
        )}
      </div>
    </div>
  );
}
