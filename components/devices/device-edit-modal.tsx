import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
// Reuse the shared EditDeviceForm component from the root components folder
import { EditDeviceForm } from '@/components/devices/edit-device-form';
import type { DeviceData } from '@/types/device';
import { toast } from 'sonner';

interface DeviceEditModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  device?: DeviceData;
}

export function DeviceEditModal({ open, onOpenChange, device }: DeviceEditModalProps) {
  const handleSuccess = () => {
    toast.success('Device updated successfully');
    onOpenChange(false);
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl w-full bg-sidebar border-sidebar-border text-white">
        <DialogHeader>
          <DialogTitle>Edit Device</DialogTitle>
          <DialogDescription className="text-[#94a3b8]">
            Update the quantum device information.
          </DialogDescription>
        </DialogHeader>
        <div className="max-h-[80vh] overflow-y-auto pr-2">
          <EditDeviceForm deviceData={device} isEdit onSuccess={handleSuccess} />
        </div>
      </DialogContent>
    </Dialog>
  );
}
