import { Card, CardContent } from '@/components/ui/card';

interface CalibrationMetric {
  name: string;
  median: string;
  min?: string;
  max?: string;
  color: string;
  value: number; // 0-100 percentage for progress bar
}

interface CalibrationMetricsProps {
  metrics: CalibrationMetric[];
}

export function CalibrationMetrics({ metrics }: CalibrationMetricsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {metrics.map((metric) => (
        <Card key={metric.name} className="bg-[#262131] border-[#3b3b3b]">
          <CardContent className="p-6">
            <h3 className="text-white font-medium mb-6">{metric.name}</h3>

            <div className="space-y-2 mb-6">
              <div className="flex justify-between">
                <span className="text-[#94a3b8] text-sm">Median</span>
                <span className="text-white font-medium">{metric.median}</span>
              </div>

              {metric.min && (
                <div className="flex justify-between">
                  <span className="text-[#94a3b8] text-sm">Min</span>
                  <span className="text-white font-medium">{metric.min}</span>
                </div>
              )}

              {metric.max && (
                <div className="flex justify-between">
                  <span className="text-[#94a3b8] text-sm">Max</span>
                  <span className="text-white font-medium">{metric.max}</span>
                </div>
              )}
            </div>

            <div className="w-full h-2 bg-[#1d1825] rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full`}
                style={{
                  width: `${metric.value}%`,
                  backgroundColor: metric.color,
                }}
              />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
