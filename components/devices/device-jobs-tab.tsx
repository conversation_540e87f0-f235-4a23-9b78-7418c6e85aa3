/**
 * DeviceJobsTab component
 * -----------------------
 * This module defines the Jobs Info tab for the device card.
 * It fetches and displays a summary and table of quantum jobs for a device,
 * including charts, job statistics, and a searchable jobs table.
 * Individual job rows are rendered using the JobsRow component.
 *
 * All API fetching and state management for jobs data is handled here.
 * The functionality on this tab is different from all other tabs, which is why it is separate.
 */

import React, { useMemo, useState } from 'react';
import { DollarSign, Zap, ListChecks, Banknote, Filter, Download } from 'lucide-react';
import { Pie<PERSON>hart as Re<PERSON>ie<PERSON>hart, Pie, Cell, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent } from '@/components/ui/card';
import { PercentBar } from '@/components/ui/percent-bar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { useJobsForDevice } from '@/hooks/use-api';
import type { JobsRowProps } from '@/types/jobs';
import { ColumnDef } from '@tanstack/react-table';

// Color map for job types
const JOB_TYPE_COLORS: Record<string, string> = {
  gate_model: '#8a2be2',
  ahs: '#ef4444',
  simulation: '#22c55e',
  hybrid: '#eab308',
  annealing: '#06b6d4',
  photonic: '#f59e42',
  other: '#64748b',
  unknown: '#94a3b8',
};

const JOB_STATUSES = ['Completed', 'Running', 'Queued', 'Unsuccessful'];

function getJobTypeDistribution(jobsArray: JobsRowProps[]) {
  const typeCounts: Record<string, number> = {};
  jobsArray.forEach((job) => {
    const type = job.experimentType || 'unknown';
    typeCounts[type] = (typeCounts[type] || 0) + 1;
  });
  const total = jobsArray.length;
  return Object.entries(typeCounts).map(([type, count]) => ({
    name: type,
    value: count,
    percent: total > 0 ? ((count / total) * 100).toFixed(2) : '0.00',
  }));
}

function isStatusMatch(status?: string, filter?: string) {
  return (
    filter === 'All Statuses' ||
    status?.toLowerCase() === filter?.toLowerCase() ||
    (filter === 'Unsuccessful' &&
      (status?.toLowerCase() === 'failed' || status?.toLowerCase() === 'cancelled'))
  );
}

export function DeviceJobsTab({
  provider = 'qbraid',
  deviceId = 'qbraid_qir_simulator',
}: {
  provider?: string;
  deviceId?: string;
}) {
  const [jobsFilter, setJobsFilter] = useState<string>('All Statuses');
  const [page, setPage] = useState(0);
  const [resultsPerPage, setResultsPerPage] = useState(10);

  const normalizedProvider = provider?.toLowerCase?.() ?? provider;
  // Fetch a large set of jobs once; we'll paginate client-side
  const { data, isLoading } = useJobsForDevice(normalizedProvider, deviceId, 0, 1000);
  const jobsArray: JobsRowProps[] = Array.isArray((data as any)?.jobsArray)
    ? (data as any).jobsArray
    : [];

  const filteredJobs = useMemo(
    () =>
      jobsArray.filter((job) => {
        if (job.qbraidDeviceId === 'No jobs found' || job.qbraidDeviceId === 'null-device') {
          return false;
        }
        return isStatusMatch(job.qbraidStatus, jobsFilter);
      }),
    [jobsArray, jobsFilter],
  );

  const totalJobs = filteredJobs.length;

  // Paginate client-side
  const paginatedJobs = useMemo(
    () => filteredJobs.slice(page * resultsPerPage, (page + 1) * resultsPerPage),
    [filteredJobs, page, resultsPerPage],
  );

  const averageCost = useMemo(() => {
    if (filteredJobs.length === 0) return 0;
    const sum = filteredJobs.reduce((acc, j) => acc + (j.cost || 0), 0);
    return sum / filteredJobs.length;
  }, [filteredJobs]);

  const averageCostUSD = averageCost / 100;

  const totalCostCredits = filteredJobs.reduce((acc, j) => acc + (j.cost || 0), 0);
  const totalCostUSD = totalCostCredits / 100;

  const averageShots = useMemo(() => {
    if (filteredJobs.length === 0) return 0;
    const sum = filteredJobs.reduce((acc, j) => acc + (j.shots || 0), 0);
    return sum / filteredJobs.length;
  }, [filteredJobs]);

  const jobTypeData = useMemo(() => {
    const data = getJobTypeDistribution(filteredJobs);
    // If no jobs (or no types) exist, show a single placeholder slice so the chart renders.
    return data.length > 0 ? data : [{ name: 'unknown', value: 1, percent: '100.00' }];
  }, [filteredJobs]);

  /* ------------------------------ DataTable ------------------------------ */
  const tableData = paginatedJobs.map((job, idx) => ({
    id: page * resultsPerPage + idx + 1,
    ...job,
  }));

  function formatDate(date?: Date | string) {
    if (!date) return 'N/A';
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleString();
  }

  const columns: ColumnDef<JobsRowProps & { id: number }>[] = [
    { accessorKey: 'qbraidDeviceId', header: 'Device ID (qBraid)' },
    {
      id: 'createdEnded',
      header: 'Created At / Ended At',
      cell: ({ row }) => (
        <div className="flex flex-col text-xs text-muted-foreground">
          <span>{formatDate(row.original.timeStamps?.createdAt)}</span>
          <span>{formatDate(row.original.timeStamps?.endedAt)}</span>
        </div>
      ),
    },
    {
      accessorKey: 'queuePosition',
      header: 'Queue Position',
      cell: ({ row }) => row.original.queuePosition ?? 'N/A',
    },
    { accessorKey: 'qbraidStatus', header: 'Job Status' },
    { accessorKey: 'vendor', header: 'Vendor' },
    { accessorKey: 'provider', header: 'Provider' },
    {
      accessorKey: 'shots',
      header: 'Shots',
      cell: ({ row }) => row.original.shots?.toLocaleString() ?? 'N/A',
    },
    {
      accessorKey: 'cost',
      header: 'Cost (Credits)',
      cell: ({ row }) => (row.original.cost !== undefined ? row.original.cost?.toFixed(4) : 'N/A'),
    },
    { accessorKey: 'experimentType', header: 'Experiment Type' },
  ];

  /* --------------------------- Export Functions -------------------------- */
  const exportToPDF = async () => {
    try {
      // @ts-ignore  Dynamic import of ESM module without type declarations
      const jsPDFModule = await import('jspdf');
      // @ts-ignore  Dynamic import of commonjs module without type declarations
      const autoTableModule = await import('jspdf-autotable');

      const doc = new jsPDFModule.jsPDF({ orientation: 'landscape' });

      const body = tableData.map((j) => [
        j.qbraidDeviceId,
        formatDate(j.timeStamps?.createdAt),
        formatDate(j.timeStamps?.endedAt),
        j.queuePosition ?? 'N/A',
        j.qbraidStatus ?? 'N/A',
        j.vendor ?? 'N/A',
        j.provider ?? 'N/A',
        j.shots ?? 'N/A',
        j.cost ?? 'N/A',
        j.experimentType ?? 'N/A',
      ]);

      autoTableModule.default(doc, {
        head: [
          [
            'Device ID (qBraid)',
            'Created At',
            'Ended At',
            'Queue Position',
            'Job Status',
            'Vendor',
            'Provider',
            'Shots',
            'Cost (Credits)',
            'Experiment Type',
          ],
        ],
        body,
        styles: { fontSize: 8 },
      });
      doc.save(`jobs_${deviceId}.pdf`);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('PDF export failed', err);
    }
  };

  /* ------------------------------- Loading ------------------------------- */
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10 text-muted-foreground">Loading...</div>
    );
  }

  return (
    <div className="space-y-6">
      {/* First Row: Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-sidebar border-sidebar-border">
          <CardContent className="p-4 flex items-center gap-4">
            <DollarSign className="size-5 text-[#8a2be2]" />
            <div>
              <p className="text-sm text-muted-foreground">Avg Cost per Task (USD)</p>
              <p className="text-xl font-semibold text-white">${averageCostUSD.toFixed(4)}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-sidebar border-sidebar-border">
          <CardContent className="p-4 flex items-center gap-4">
            <Zap className="size-5 text-[#8a2be2]" />
            <div>
              <p className="text-sm text-muted-foreground">Avg Shots per Task</p>
              <p className="text-xl font-semibold text-white">{averageShots.toFixed(2)}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-sidebar border-sidebar-border">
          <CardContent className="p-4 flex items-center gap-4">
            <ListChecks className="size-5 text-[#8a2be2]" />
            <div>
              <p className="text-sm text-muted-foreground">Total Tasks</p>
              <p className="text-xl font-semibold text-white">{totalJobs}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-sidebar border-sidebar-border">
          <CardContent className="p-4 flex items-center gap-4">
            <Banknote className="size-5 text-[#8a2be2]" />
            <div>
              <p className="text-sm text-muted-foreground">Total Job Value (USD)</p>
              <p className="text-xl font-semibold text-white">${totalCostUSD.toFixed(4)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Second Row: Status and Type Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Jobs by Status */}
        <Card className="bg-sidebar border-sidebar-border">
          <CardContent className="p-4 space-y-3">
            <h4 className="text-lg font-semibold mb-2">Jobs by Status</h4>
            {/* Compute percentages */}
            {(() => {
              const counts = filteredJobs.reduce(
                (acc, job) => {
                  if (job.qbraidStatus === 'COMPLETED') acc.completed++;
                  else if (job.qbraidStatus === 'RUNNING') acc.running++;
                  else if (job.qbraidStatus === 'QUEUED') acc.queued++;
                  else if (['FAILED', 'CANCELLED', 'UNKNOWN'].includes(job.qbraidStatus || ''))
                    acc.unsuccessful++;
                  return acc;
                },
                { completed: 0, running: 0, queued: 0, unsuccessful: 0 },
              );
              const total = totalJobs || 1;
              return (
                <div className="space-y-2">
                  <PercentBar
                    name="Completed"
                    percentage={`${((counts.completed / total) * 100).toFixed(2)}%`}
                    color="bg-[#22c55e]"
                  />
                  <PercentBar
                    name="Running"
                    percentage={`${((counts.running / total) * 100).toFixed(2)}%`}
                    color="bg-[#3b82f6]"
                  />
                  <PercentBar
                    name="Queued"
                    percentage={`${((counts.queued / total) * 100).toFixed(2)}%`}
                    color="bg-[#eab308]"
                  />
                  <PercentBar
                    name="Unsuccessful"
                    percentage={`${((counts.unsuccessful / total) * 100).toFixed(2)}%`}
                    color="bg-[#94a3b8]"
                  />
                </div>
              );
            })()}
          </CardContent>
        </Card>

        {/* Jobs by Type Pie Chart */}
        <Card className="bg-sidebar border-sidebar-border">
          <CardContent className="p-4">
            <h4 className="text-lg font-semibold mb-4">Jobs by Type</h4>
            <div className="flex items-center gap-6">
              <div className="relative w-48 h-40 mx-auto">
                <ResponsiveContainer width="100%" height="100%">
                  <RePieChart>
                    <Pie
                      data={jobTypeData}
                      dataKey="value"
                      nameKey="name"
                      cx="60%"
                      cy="50%"
                      outerRadius={70}
                    >
                      {jobTypeData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={JOB_TYPE_COLORS[entry.name] || '#94a3b8'}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number, name: string, props: any) => [
                        `${value}`,
                        `${props.payload.name}`,
                      ]}
                    />
                  </RePieChart>
                </ResponsiveContainer>
              </div>
              {/* Legend */}
              <div className="space-y-2 text-sm">
                {jobTypeData.map((entry) => (
                  <div key={entry.name} className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: JOB_TYPE_COLORS[entry.name] || '#8884d8' }}
                    ></div>
                    <span className="text-[#94a3b8]">
                      {entry.name} ({entry.percent}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Toolbar & DataTable */}
      <div className="space-y-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Status Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="bg-sidebar border-sidebar-border text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
              >
                <Filter className="w-4 h-4 mr-2" />
                {jobsFilter}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-sidebar border-sidebar-border">
              <DropdownMenuItem onClick={() => setJobsFilter('All Statuses')}>
                All Statuses
              </DropdownMenuItem>
              {JOB_STATUSES.map((status) => (
                <DropdownMenuItem key={status} onClick={() => setJobsFilter(status)}>
                  {status}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Export */}
          <Button
            variant="outline"
            size="sm"
            onClick={exportToPDF}
            className="bg-sidebar border-sidebar-border text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
          >
            <Download className="w-4 h-4 mr-2" /> Export PDF
          </Button>
        </div>

        {/* DataTable & Pagination */}
        <DataTable data={tableData} columnsOverride={columns} hideToolbar isLoading={isLoading} />

        <div className="flex items-center justify-between pt-2">
          <div className="text-sm text-muted-foreground">
            Page {page + 1} of {Math.max(1, Math.ceil(totalJobs / resultsPerPage))}
          </div>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((p) => Math.max(0, p - 1))}
              disabled={page === 0}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((p) => p + 1)}
              disabled={(page + 1) * resultsPerPage >= totalJobs}
            >
              Next
            </Button>
            <select
              className="px-2 py-1 rounded bg-sidebar border-sidebar-border text-white text-sm"
              value={resultsPerPage}
              onChange={(e) => {
                setResultsPerPage(Number(e.target.value));
                setPage(0);
              }}
            >
              {[5, 10, 20, 50].map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}
