'use client';

import { usePathname } from 'next/navigation';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';

export function SiteHeader() {
  const pathname = usePathname();
  const segment = pathname.split('/')[1] || 'dashboard';

  const titleMap: Record<string, string> = {
    '': 'Dashboard',
    devices: 'Devices',
    team: 'Team',
  };

  const pageTitle = titleMap[segment] ?? segment.charAt(0).toUpperCase() + segment.slice(1);

  return (
    <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mx-2 data-[orientation=vertical]:h-4" />
        <h1 className="text-base font-medium capitalize">{pageTitle}</h1>
        <div className="ml-auto flex items-center">
          {/* qBraid logo */}
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img src="/qbraid_logo.png" alt="qBraid" className="h-6 w-auto" />
        </div>
      </div>
    </header>
  );
}
