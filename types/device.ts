// Device-related types for use across the app

/**
 * Represents a quantum device and its properties.
 */
export interface DeviceData {
  name: string;
  type: string;
  paradigm: string;
  architecture: string;
  processorType: string;
  numberQubits: string;
  deviceId: string;
  qbraid_id: string;
  runPackage: string;
  runInputTypes: string[];
  noiseModels: string[];
  status: string;
  visibility: string;
  whiteListedDomains: string[];
  blackListedDomains: string[];
  pricing: {
    perMinute: string;
    perTask: string;
    perShot: string;
  };
  deviceDescription: string;
  deviceAboutUrl: string;
  providerDescription: string;
  deviceImage: string;
  technicalSpecifications: string;
  providerLogoLight: string;
  providerLogoDark: string;
  isAvailable: string;
}

/**
 * Props for the DeviceCard component.
 */
export interface DeviceCardProps {
  qbraid_id: string;
  name: string;
  type: string;
  paradigm: string;
  deviceDescription: string;
  numberQubits: string;
  status: string;
  provider: string;
  architecture: string;
  processorType: string;
  pricing: {
    perMinute: string;
    perTask: string;
    perShot: string;
  };
  vendor: string;
  pendingJobs: number;
  runPackage: string;
  noiseModels: string[];
  defaultTab?: string;
  /**
   * Optional handler to trigger when the user clicks the “Edit” action. This is primarily
   * used by the devices listing page to open the edit-device modal.
   */
  onEdit?: () => void;
}
