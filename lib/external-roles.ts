import { ExternalRoleResponse } from '@/types/auth';
import { getRedisClient, isRedisHealthy } from './redis';

// Cache configuration optimized for 19k users (internal constants)
const ROLES_CACHE_PREFIX = 'orgroles:'; // Changed to use email-based org roles structure
const ROLES_CACHE_TTL = 21600; // 6 hours in seconds (reduced API calls)
const ROLES_BATCH_SIZE = 50; // Batch size for bulk operations
const API_RATE_LIMIT_DELAY = 100; // 100ms between API calls to prevent rate limiting

/**
 * Enhanced organization role structure for multi-org support
 */
interface UserOrgRole {
  orgId: string;
  orgName: string;
  role: string;
  updated: string; // ISO timestamp
}

export interface UserOrgRoles {
  [orgId: string]: UserOrgRole;
}

/**
 * Organization structure from QBraid API
 */
interface Organization {
  org: {
    role: string;
    email: string;
    organization: {
      name: string;
      _id: string;
    };
  };
}

interface OrganizationsResponse {
  organizations: Organization[];
  pagination: {
    currentPage: number;
    limit: string;
    totalPages: number;
    totalOrganizations: number;
  };
}

/**
 * Rate limiting utility to prevent API overload with 19k users
 */
async function rateLimitedDelay(): Promise<void> {
  await new Promise((resolve) => setTimeout(resolve, API_RATE_LIMIT_DELAY));
}

/**
 * Process organizations data and extract user roles
 * This function receives data from API routes instead of making HTTP calls
 */
export async function processOrganizationsForRoles(
  email: string,
  organizationsData: OrganizationsResponse,
): Promise<string[]> {
  console.log(`🔍 [EXTERNAL-ROLES] ========== STARTING ROLE PROCESSING ==========`);
  console.log(`🔍 [EXTERNAL-ROLES] Processing org data for user: ${email}`);
  console.log(`🔍 [EXTERNAL-ROLES] Input data structure:`, {
    hasOrganizations: !!organizationsData?.organizations,
    organizationsCount: organizationsData?.organizations?.length || 0,
    hasPagination: !!organizationsData?.pagination,
    totalOrganizations: organizationsData?.pagination?.totalOrganizations || 0,
  });

  try {
    const organizations = organizationsData.organizations || [];
    console.log(`🏢 [EXTERNAL-ROLES] Found ${organizations.length} organizations to check`);

    if (organizations.length === 0) {
      console.log(`⚠️ [EXTERNAL-ROLES] No organizations found in data`);
      return [];
    }

    const userRoles: string[] = [];
    const userOrgRoles: UserOrgRoles = {}; // Store detailed org info

    // Log detailed info about each organization for debugging
    console.log(`🔍 [EXTERNAL-ROLES] Analyzing organizations for user ${email}:`);
    organizations.forEach((org, index) => {
      console.log(`🏢 [EXTERNAL-ROLES] Organization ${index + 1}:`, {
        hasOrg: !!org.org,
        hasOrgData: !!org.org?.organization,
        orgId: org.org?.organization?._id || 'missing',
        orgName: org.org?.organization?.name || 'missing',
        userRole: org.org?.role || 'missing',
        userEmail: org.org?.email || 'missing',
        isTargetUser: org.org?.email?.toLowerCase() === email.toLowerCase(),
      });
    });

    // For each organization, check if the user has access and get their role
    for (let i = 0; i < organizations.length; i++) {
      const org = organizations[i];
      console.log(
        `🔍 [EXTERNAL-ROLES] Processing organization ${i + 1}/${organizations.length}...`,
      );

      try {
        const orgId = org.org?.organization?._id;
        const orgName = org.org?.organization?.name;
        const userRole = org.org?.role;
        const userEmail = org.org?.email;

        console.log(`📋 [EXTERNAL-ROLES] Organization ${i + 1} details:`, {
          orgId: orgId || 'MISSING',
          orgName: orgName || 'MISSING',
          userRole: userRole || 'MISSING',
          userEmail: userEmail || 'MISSING',
          targetEmail: email,
          emailsMatch: userEmail?.toLowerCase() === email.toLowerCase(),
        });

        if (!orgId || !orgName || !userRole || !userEmail) {
          console.log(
            `⚠️ [EXTERNAL-ROLES] Skipping organization ${i + 1} - missing required data:`,
            {
              hasOrgId: !!orgId,
              hasOrgName: !!orgName,
              hasUserRole: !!userRole,
              hasUserEmail: !!userEmail,
            },
          );
          continue;
        }

        console.log(
          `🔍 [EXTERNAL-ROLES] Checking org ${orgId.substring(0, 10)}... for user ${email}...`,
        );

        // Check if this organization entry is for the current user
        if (userEmail.toLowerCase() === email.toLowerCase()) {
          userRoles.push(userRole);

          // Store detailed org information
          userOrgRoles[orgId] = {
            orgId,
            orgName,
            role: userRole,
            updated: new Date().toISOString(),
          };

          console.log(
            `✅ [EXTERNAL-ROLES] MATCH FOUND! User ${email} has role "${userRole}" in org ${orgName} (${orgId.substring(0, 10)}...)`,
          );
          console.log(
            `✅ [EXTERNAL-ROLES] Role added to collection. Current roles: [${userRoles.join(', ')}]`,
          );
        } else {
          console.log(`❌ [EXTERNAL-ROLES] No match - organization entry is for different user:`, {
            orgUserEmail: userEmail,
            targetUserEmail: email,
            orgRole: userRole,
          });
        }

        // Small delay between org checks to prevent rate limiting
        await new Promise((resolve) => setTimeout(resolve, 10));
      } catch (orgError: any) {
        console.warn(`⚠️ [EXTERNAL-ROLES] Failed to process organization ${i + 1}:`, {
          error: orgError.message,
          orgData: org,
        });
        // Continue with other organizations
      }
    }

    // Remove duplicates and ensure we have valid roles
    const uniqueRoles = [...new Set(userRoles)].filter((role) => role && role.length > 0);

    console.log(`📊 [EXTERNAL-ROLES] Processing complete:`, {
      totalOrganizations: organizations.length,
      matchingOrganizations: userRoles.length,
      uniqueRoles: uniqueRoles.length,
      duplicatesRemoved: userRoles.length - uniqueRoles.length,
      detailedOrgRoles: userOrgRoles,
    });

    // Cache the detailed org roles using email-based caching
    if (Object.keys(userOrgRoles).length > 0) {
      console.log(`💾 [EXTERNAL-ROLES] Caching detailed org roles for ${email}...`);
      try {
        await cacheOrgRoles(email, userOrgRoles);
        console.log(
          `✅ [EXTERNAL-ROLES] Successfully cached ${Object.keys(userOrgRoles).length} org roles for ${email}`,
        );
      } catch (cacheError: any) {
        console.warn(`⚠️ [EXTERNAL-ROLES] Failed to cache org roles, but continuing:`, {
          error: cacheError.message,
          email: email,
          orgCount: Object.keys(userOrgRoles).length,
        });
      }
    }

    console.log(`✅ [EXTERNAL-ROLES] Final result for ${email}:`, {
      rolesCount: uniqueRoles.length,
      roles: uniqueRoles,
      orgRolesCount: Object.keys(userOrgRoles).length,
      orgRoles: userOrgRoles,
    });

    console.log(`✅ [EXTERNAL-ROLES] ========== ROLE PROCESSING COMPLETED ==========`);

    return uniqueRoles;
  } catch (error: any) {
    console.error(`❌ [EXTERNAL-ROLES] ========== ROLE PROCESSING FAILED ==========`);
    console.error(`❌ [EXTERNAL-ROLES] Error processing org data for ${email}:`, {
      errorMessage: error.message,
      errorStack: error.stack,
      inputData: organizationsData,
    });
    return [];
  }
}

/**
 * Process organizations data and extract user org-specific roles
 * This function receives data from API routes instead of making HTTP calls
 */
export async function processOrganizationsForOrgRoles(
  email: string,
  organizationsData: OrganizationsResponse,
): Promise<UserOrgRoles> {
  try {
    console.log(`🔍 [ORG-ROLES] Processing org data for org-specific roles: ${email}`);

    const organizations = organizationsData.organizations || [];
    console.log(`🏢 [ORG-ROLES] Found ${organizations.length} organizations to check`);

    const userOrgRoles: UserOrgRoles = {};

    // Check each organization for user's role
    for (const org of organizations) {
      try {
        const orgId = org.org?.organization?._id;
        const orgName = org.org?.organization?.name || 'Unknown Organization';
        const userRole = org.org?.role;
        const userEmail = org.org?.email;

        if (!orgId || !userRole || !userEmail) continue;

        console.log(`🔍 [ORG-ROLES] Checking org ${orgId} (${orgName})...`);

        // Check if this organization entry is for the current user
        if (userEmail.toLowerCase() === email.toLowerCase()) {
          userOrgRoles[orgId] = {
            orgId,
            orgName,
            role: userRole,
            updated: new Date().toISOString(),
          };

          console.log(`✅ [ORG-ROLES] Found role "${userRole}" in org ${orgName} (${orgId})`);
        }

        await new Promise((resolve) => setTimeout(resolve, 10)); // Rate limiting
      } catch (orgError: any) {
        console.warn(`⚠️ [ORG-ROLES] Failed to process org:`, orgError.message);
      }
    }

    console.log(
      `✅ [ORG-ROLES] Processed roles in ${Object.keys(userOrgRoles).length} organizations for ${email}`,
    );
    return userOrgRoles;
  } catch (error: any) {
    console.error(`❌ [ORG-ROLES] Failed to process org roles for ${email}:`, error);
    return {};
  }
}

/**
 * Get cached org roles from Redis using email
 */
export async function getCachedOrgRoles(email: string): Promise<UserOrgRoles | null> {
  console.log(`🔍 [REDIS-CACHE] ========== CHECKING ORG ROLES CACHE ==========`);
  console.log(`🔍 [REDIS-CACHE] Looking for cached org roles for user: ${email}`);

  if (!(await isRedisHealthy())) {
    console.log(`❌ [REDIS-CACHE] Redis not healthy, skipping cache check`);
    return null;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email}`;
    console.log(`🔍 [REDIS-CACHE] Cache key: ${cacheKey}`);

    const cacheStart = Date.now();
    const cached = await redis.get(cacheKey);
    const cacheTime = Date.now() - cacheStart;

    console.log(`📋 [REDIS-CACHE] Redis GET operation took ${cacheTime}ms`);

    if (cached) {
      try {
        const orgRoles = JSON.parse(cached) as UserOrgRoles;
        const roleCount = Object.keys(orgRoles).length;
        const simpleRoles = Object.values(orgRoles).map((org) => org.role);

        console.log(`💾 [REDIS-CACHE] Cache HIT for user ${email}:`, {
          orgCount: roleCount,
          orgRoles: orgRoles,
          simpleRoles: simpleRoles,
          cacheTime: `${cacheTime}ms`,
          cacheKey: cacheKey,
        });
        console.log(
          `✅ [REDIS-CACHE] ========== CACHE HIT - RETURNING CACHED ORG ROLES ==========`,
        );
        return orgRoles;
      } catch (parseError: any) {
        console.error(`❌ [REDIS-CACHE] Failed to parse cached org roles data:`, {
          error: parseError.message,
          rawData: cached.substring(0, 100) + '...',
          email: email,
        });
        return null;
      }
    }

    console.log(`🔍 [REDIS-CACHE] Cache MISS for user ${email}`);
    console.log(`⚠️ [REDIS-CACHE] ========== CACHE MISS - NO CACHED ORG ROLES FOUND ==========`);
    return null;
  } catch (error: any) {
    console.error(`❌ [REDIS-CACHE] Failed to get cached org roles for user ${email}:`, {
      errorMessage: error.message,
      errorStack: error.stack,
      cacheKey: `${ROLES_CACHE_PREFIX}${email}`,
    });
    return null;
  }
}

/**
 * Cache org roles in Redis using email with detailed organization information
 */
async function cacheOrgRoles(
  email: string,
  orgRoles: UserOrgRoles,
  ttl: number = ROLES_CACHE_TTL,
): Promise<void> {
  console.log(`🔍 [REDIS-CACHE] ========== CACHING ORG ROLES ==========`);
  console.log(`🔍 [REDIS-CACHE] Caching org roles for user: ${email}`);

  const orgCount = Object.keys(orgRoles).length;
  const simpleRoles = Object.values(orgRoles).map((org) => org.role);

  console.log(`🔍 [REDIS-CACHE] Cache details:`, {
    email: email,
    orgCount: orgCount,
    orgRoles: orgRoles,
    simpleRoles: simpleRoles,
    ttlSeconds: ttl,
  });

  if (!(await isRedisHealthy())) {
    console.log(`❌ [REDIS-CACHE] Redis not healthy, skipping cache operation`);
    return;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email}`;
    console.log(`🔍 [REDIS-CACHE] Cache key: ${cacheKey}`);

    console.log(`🔍 [REDIS-CACHE] Storing detailed org roles structure:`, orgRoles);

    const cacheStart = Date.now();
    await redis.setex(cacheKey, ttl, JSON.stringify(orgRoles));
    const cacheTime = Date.now() - cacheStart;

    console.log(`💾 [REDIS-CACHE] Redis SETEX operation took ${cacheTime}ms`);
    console.log(
      `💾 [REDIS-CACHE] Successfully cached ${orgCount} org roles for user ${email} (TTL: ${ttl}s)`,
    );
    console.log(`✅ [REDIS-CACHE] ========== ORG ROLES CACHED SUCCESSFULLY ==========`);
  } catch (error: any) {
    console.error(`❌ [REDIS-CACHE] Failed to cache org roles for user ${email}:`, {
      errorMessage: error.message,
      errorStack: error.stack,
      email: email,
      orgCount: orgCount,
      cacheKey: `${ROLES_CACHE_PREFIX}${email}`,
    });
  }
}

/**
 * Get roles with caching - now uses email-based caching
 */
export async function getRolesForUser(
  email: string,
  forceRefresh: boolean = false,
  organizationsData?: OrganizationsResponse,
  sessionId?: string,
): Promise<string[]> {
  console.log(`🔍 [ROLES-FLOW] ========== GET ROLES FOR USER ==========`);
  console.log(`🔍 [ROLES-FLOW] Parameters:`, {
    email: email,
    forceRefresh: forceRefresh,
    hasOrganizationsData: !!organizationsData,
    hasSessionId: !!sessionId,
    sessionIdPrefix: sessionId?.substring(0, 10) + '...' || 'none',
  });

  // Check cache first if force refresh is not requested
  if (!forceRefresh) {
    console.log(`🔍 [ROLES-FLOW] Checking cache (forceRefresh: ${forceRefresh})`);
    const cachedRoles = await getCachedOrgRoles(email);
    if (cachedRoles !== null) {
      console.log(`✅ [ROLES-FLOW] Using cached roles, returning early`);
      console.log(`✅ [ROLES-FLOW] ========== RETURNING CACHED ROLES ==========`);
      return flattenOrgRoles(cachedRoles);
    }
  } else {
    console.log(`🔍 [ROLES-FLOW] Skipping cache check: Force refresh requested`);
  }

  // Process organizations data if provided
  let roles: string[] = [];
  let orgRoles: UserOrgRoles = {};

  if (organizationsData) {
    console.log(`🔍 [ROLES-FLOW] Processing organizations data...`);
    roles = await processOrganizationsForRoles(email, organizationsData);
    orgRoles = await processOrganizationsForOrgRoles(email, organizationsData);

    console.log(`📥 [ROLES-FLOW] Organization processing result:`, {
      rolesCount: roles.length,
      roles: roles,
      orgRolesCount: Object.keys(orgRoles).length,
    });
  } else {
    console.log(`⚠️ [ROLES-FLOW] No organizations data provided, returning empty roles`);
  }

  // Cache the org roles result if we have data (even if empty to prevent repeated API calls)
  if (Object.keys(orgRoles).length > 0) {
    console.log(`🔍 [ROLES-FLOW] Caching org roles for future requests...`);
    await cacheOrgRoles(email, orgRoles);
  } else {
    console.log(`⚠️ [ROLES-FLOW] No org roles to cache`);
  }

  console.log(`✅ [ROLES-FLOW] Final result:`, {
    email: email,
    rolesCount: roles.length,
    roles: roles,
    orgRolesCount: Object.keys(orgRoles).length,
  });
  console.log(`✅ [ROLES-FLOW] ========== ROLES FLOW COMPLETED ==========`);

  return roles;
}

/**
 * Exponential backoff retry utility for handling rate limits with 19k users
 */
export async function exponentialBackoff(
  fn: () => Promise<any>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
): Promise<any> {
  let lastError: Error | undefined;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      // Don't retry on non-rate-limit errors
      if (error.status !== 429 && error.status !== 503) {
        throw error;
      }

      if (attempt === maxRetries) {
        break;
      }

      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      console.log(
        `⏰ [EXTERNAL-ROLES] Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`,
      );
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError || new Error('Max retries exceeded');
}

/**
 * Get cache statistics for monitoring high-volume usage (19k users)
 */
export async function getCacheStats(): Promise<{
  totalKeys: number;
  cacheSize: string;
  ttl: number;
  healthStatus: string;
} | null> {
  if (!(await isRedisHealthy())) {
    return null;
  }

  try {
    const redis = getRedisClient();

    // Count role cache keys (both legacy and org-specific)
    const legacyKeys = await redis.keys(`orgroles:*`);
    const totalKeys = legacyKeys.length;

    // Get approximate cache size (Redis memory usage would be better in production)
    let totalBytes = 0;
    const allKeys = legacyKeys;
    const sampleSize = Math.min(10, allKeys.length);

    for (let i = 0; i < sampleSize; i++) {
      const value = await redis.get(allKeys[i]);
      if (value) {
        totalBytes += value.length;
      }
    }

    const avgSize = totalBytes / sampleSize || 0;
    const estimatedTotalSize = avgSize * totalKeys;

    return {
      totalKeys,
      cacheSize: formatBytes(estimatedTotalSize),
      ttl: ROLES_CACHE_TTL,
      healthStatus: 'healthy',
    };
  } catch (error) {
    console.error('❌ [EXTERNAL-ROLES] Failed to get cache stats:', error);
    return {
      totalKeys: 0,
      cacheSize: '0 B',
      ttl: ROLES_CACHE_TTL,
      healthStatus: 'error',
    };
  }
}

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes: number): string {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
}

/**
 * Bulk invalidate cache for multiple users (useful for mass updates)
 */
export async function bulkInvalidateUserRoles(emails: string[]): Promise<{
  success: number;
  failed: number;
  errors: string[];
}> {
  if (!(await isRedisHealthy())) {
    return { success: 0, failed: emails.length, errors: ['Redis not healthy'] };
  }

  const results = { success: 0, failed: 0, errors: [] as string[] };

  try {
    const redis = getRedisClient();

    // Process in batches to prevent overwhelming Redis
    const batchSize = ROLES_BATCH_SIZE;

    for (let i = 0; i < emails.length; i += batchSize) {
      const batch = emails.slice(i, i + batchSize);

      // Create cache keys for org-specific caches
      const orgCacheKeys = batch.map((email) => `${ROLES_CACHE_PREFIX}${email.toLowerCase()}`);
      const allKeys = [...orgCacheKeys];

      try {
        const deleted = await redis.del(...allKeys);
        results.success += Math.min(deleted, batch.length); // Count per user, not per key
        console.log(
          `🗑️ [EXTERNAL-ROLES] Bulk invalidated cached org roles for ${batch.length} users (batch ${Math.floor(i / batchSize) + 1})`,
        );
      } catch (batchError) {
        results.failed += batch.length;
        results.errors.push(
          `Batch ${Math.floor(i / batchSize) + 1}: ${batchError instanceof Error ? batchError.message : 'Unknown error'}`,
        );
      }

      // Small delay between batches
      if (i + batchSize < emails.length) {
        await new Promise((resolve) => setTimeout(resolve, 50));
      }
    }
  } catch (error) {
    results.failed = emails.length;
    results.errors.push(error instanceof Error ? error.message : 'Unknown error');
  }

  return results;
}

/**
 * Invalidate cache for a single user's roles
 * Clears both org-specific and legacy role caches
 */
export async function invalidateUserRoles(email: string): Promise<void> {
  if (!(await isRedisHealthy())) {
    console.warn(`⚠️ [EXTERNAL-ROLES] Redis not healthy, cannot invalidate cache for ${email}`);
    return;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email.toLowerCase()}`;

    const deleted = await redis.del(cacheKey);

    console.log(
      `🗑️ [EXTERNAL-ROLES] Invalidated cached roles for ${email} (keys deleted: ${deleted})`,
    );
  } catch (error) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to invalidate roles for ${email}:`, error);
    throw error; // Re-throw to let caller handle
  }
}

/**
 * Optimized role fetching with org-specific caching
 * Uses Next.js API gateway instead of direct external client calls
 */
export async function fetchUserOrgRoles(email: string, idToken: string): Promise<UserOrgRoles> {
  try {
    console.log(`🔍 [ORG-ROLES] Fetching org-specific roles for: ${email} via API gateway`);

    await rateLimitedDelay();

    const baseUrl =
      process.env.NODE_ENV === 'production'
        ? process.env.NEXTAUTH_URL || 'https://partner-dashboard.qbraid.com'
        : 'http://localhost:3000';

    // Get organizations list through our API gateway
    console.log('📡 [ORG-ROLES] Getting organizations list via /api/orgs/get...');

    const orgsResponse = await fetch(`${baseUrl}/api/orgs/get/0/50`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Let the API route handle its own authentication from session
      },
    });

    if (!orgsResponse.ok) {
      throw new Error(
        `Organizations API failed: ${orgsResponse.status} ${orgsResponse.statusText}`,
      );
    }

    const orgsData = await orgsResponse.json();
    const organizations = orgsData.organizations || [];

    console.log(`🏢 [ORG-ROLES] Found ${organizations.length} organizations to check`);

    const userOrgRoles: UserOrgRoles = {};

    // Check each organization for user's role
    for (const org of organizations) {
      try {
        const orgId = org.org?.organization?._id || org._id;
        const orgName = org.org?.organization?.name || org.name || 'Unknown Organization';

        if (!orgId) continue;

        console.log(`🔍 [ORG-ROLES] Checking org ${orgId} (${orgName})...`);

        // Get users in this organization through our API gateway
        const usersResponse = await fetch(`${baseUrl}/api/orgs/users/${orgId}/0/100`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            // Let the API route handle its own authentication from session
          },
        });

        if (!usersResponse.ok) {
          console.warn(
            `⚠️ [ORG-ROLES] Failed to get users for org ${orgId}: ${usersResponse.status}`,
          );
          continue;
        }

        const usersData = await usersResponse.json();
        const orgUsers = usersData.orgUsers || [];

        // Find current user in this organization
        const userInOrg = orgUsers.find(
          (user: any) => user.email?.toLowerCase() === email.toLowerCase(),
        );

        if (userInOrg && userInOrg.role) {
          userOrgRoles[orgId] = {
            orgId,
            orgName,
            role: userInOrg.role,
            updated: new Date().toISOString(),
          };

          console.log(`✅ [ORG-ROLES] Found role "${userInOrg.role}" in org ${orgName} (${orgId})`);
        }

        await new Promise((resolve) => setTimeout(resolve, 50)); // Rate limiting
      } catch (orgError: any) {
        console.warn(`⚠️ [ORG-ROLES] Failed to check org:`, orgError.message);
      }
    }

    console.log(
      `✅ [ORG-ROLES] Retrieved roles in ${Object.keys(userOrgRoles).length} organizations for ${email}`,
    );
    return userOrgRoles;
  } catch (error: any) {
    console.error(`❌ [ORG-ROLES] Failed to fetch org roles for ${email}:`, error);
    return {};
  }
}

export async function fetchOrgUsers(orgId: string): Promise<any> {
  const baseUrl =
    process.env.NODE_ENV === 'production'
      ? process.env.NEXTAUTH_URL || 'https://partner-dashboard.qbraid.com'
      : 'http://localhost:3000';

  const orgUsers = await fetch(`${baseUrl}/api/orgs/users/${orgId}/0/100`, {
    method: 'GET',
  });
  return orgUsers;
}

/**
 * Cache user's org-specific roles in Redis
 */
async function cacheUserOrgRoles(
  email: string,
  orgRoles: UserOrgRoles,
  ttl: number = ROLES_CACHE_TTL,
): Promise<void> {
  if (!(await isRedisHealthy())) return;

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email.toLowerCase()}`;

    await redis.setex(cacheKey, ttl, JSON.stringify(orgRoles));

    console.log(
      `💾 [ORG-ROLES] Cached roles for ${email} in ${Object.keys(orgRoles).length} organizations (TTL: ${ttl}s)`,
    );
  } catch (error) {
    console.error(`❌ [ORG-ROLES] Failed to cache org roles for ${email}:`, error);
  }
}

/**
 * Get cached user's org-specific roles from Redis
 */
async function getCachedUserOrgRoles(email: string): Promise<UserOrgRoles | null> {
  if (!(await isRedisHealthy())) return null;

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email.toLowerCase()}`;
    const cached = await redis.get(cacheKey);

    if (cached) {
      const orgRoles = JSON.parse(cached);
      console.log(`💾 [ORG-ROLES] Cache HIT for ${email}:`, {
        organizationsCount: Object.keys(orgRoles).length,
        organizations: Object.keys(orgRoles),
      });
      return orgRoles;
    }

    console.log(`🔍 [ORG-ROLES] Cache MISS for ${email}`);
    return null;
  } catch (error) {
    console.error(`❌ [ORG-ROLES] Failed to get cached org roles for ${email}:`, error);
    return null;
  }
}

/**
 * Get user's org-specific roles with caching
 */
export async function getUserOrgRoles(
  email: string,
  forceRefresh: boolean = false,
  idToken: string,
): Promise<UserOrgRoles> {
  // Check cache first unless force refresh
  if (!forceRefresh) {
    const cachedRoles = await getCachedUserOrgRoles(email);
    if (cachedRoles !== null) {
      return cachedRoles;
    }
  }

  // Fetch from external API
  const orgRoles = await fetchUserOrgRoles(email, idToken);

  // Cache the result
  await cacheUserOrgRoles(email, orgRoles);

  return orgRoles;
}

/**
 * Get user's role in a specific organization
 */
export async function getUserRoleInOrg(
  email: string,
  orgId: string,
  idToken: string,
  forceRefresh: boolean = false,
): Promise<string | null> {
  const orgRoles = await getUserOrgRoles(email, forceRefresh, idToken);
  return orgRoles[orgId]?.role || null;
}

/**
 * Update role for user in specific organization (webhook support)
 */
export async function updateUserRoleInOrg(
  email: string,
  orgId: string,
  newRole: string,
  orgName?: string,
): Promise<void> {
  if (!(await isRedisHealthy())) return;

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email.toLowerCase()}`;

    // Get existing org roles
    const existingRoles = (await getCachedUserOrgRoles(email)) || {};

    // Update specific org role
    existingRoles[orgId] = {
      orgId,
      orgName: orgName || existingRoles[orgId]?.orgName || 'Unknown Organization',
      role: newRole,
      updated: new Date().toISOString(),
    };

    // Save back to cache
    await redis.setex(cacheKey, ROLES_CACHE_TTL, JSON.stringify(existingRoles));

    console.log(`🔄 [ORG-ROLES] Updated role for ${email} in org ${orgId}: ${newRole}`);
  } catch (error) {
    console.error(`❌ [ORG-ROLES] Failed to update org role for ${email}:`, error);
  }
}

/**
 * Remove user from organization (webhook support)
 */
export async function removeUserFromOrg(email: string, orgId: string): Promise<void> {
  if (!(await isRedisHealthy())) return;

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email.toLowerCase()}`;

    // Get existing org roles
    const existingRoles = (await getCachedUserOrgRoles(email)) || {};

    // Remove specific org
    delete existingRoles[orgId];

    // Save back to cache
    await redis.setex(cacheKey, ROLES_CACHE_TTL, JSON.stringify(existingRoles));

    console.log(`🗑️ [ORG-ROLES] Removed ${email} from org ${orgId}`);
  } catch (error) {
    console.error(`❌ [ORG-ROLES] Failed to remove user from org:`, error);
  }
}

/**
 * Convert org-specific roles to flat array (backward compatibility)
 */
function flattenOrgRoles(orgRoles: UserOrgRoles): string[] {
  return Object.values(orgRoles).map((org) => org.role);
}
