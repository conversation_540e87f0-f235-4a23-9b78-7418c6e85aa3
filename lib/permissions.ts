import { Permission, ExternalRoleMapping } from '@/types/auth';

// ============================================================================
// ROLE DEFINITIONS AND HIERARCHY
// ============================================================================

/**
 * Role hierarchy levels - Higher number = Higher authority
 *
 * This defines the power structure of roles in the organization.
 * Used to prevent users from promoting others above their own level.
 */
export const ROLE_HIERARCHY: Record<string, number> = {
  viewer: 1, // Read-only access
  member: 2, // Basic user with limited write access
  manager: 3, // Team management capabilities
  admin: 4, // Full administrative access
  superadmin: 5, // System-wide administration
  owner: 6, // Organization owner - highest authority
};

/**
 * Get all available role names for UI dropdowns, sorted by hierarchy
 */
export const ALL_ROLES = Object.keys(ROLE_HIERARCHY).sort(
  (a, b) => ROLE_HIERARCHY[a] - ROLE_HIERARCHY[b],
);

// ============================================================================
// PERMISSION MAPPINGS
// ============================================================================

/**
 * Maps external API roles to internal permission sets
 *
 * When adding new roles:
 * 1. Add the role name as a key
 * 2. List all permissions that role should have
 * 3. Consider inheritance - higher roles should include lower role permissions
 */
export const externalRoleToPermissions: ExternalRoleMapping = {
  // Viewer: Read-only access to most resources
  viewer: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],

  // Member: Viewer permissions + ability to edit own profile
  member: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.EditProfile, // Can edit their own profile
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],

  // Admin: Full access except ownership actions
  admin: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam, // Can manage team members
    Permission.ViewEarnings,
    Permission.ManageEarnings,
    Permission.ViewJobs,
    Permission.ManageJobs,
    Permission.AdminAccess, // Special admin-only features
  ],

  // Owner: Complete control over the organization
  owner: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewEarnings,
    Permission.ManageEarnings,
    Permission.ViewJobs,
    Permission.ManageJobs,
    Permission.AdminAccess,
    // Note: Owners implicitly have all permissions
  ],
};

// ============================================================================
// ROLE MANAGEMENT RULES
// ============================================================================

/**
 * Defines which roles each role can manage (assign/remove)
 *
 * Rules:
 * - Users can only manage roles below their hierarchy level
 * - Owners cannot be managed by anyone
 * - Members and viewers cannot manage any roles
 */
export const ROLE_MANAGEMENT_PERMISSIONS: Record<string, string[]> = {
  owner: ['viewer', 'member', 'manager', 'admin', 'superadmin'],
  superadmin: ['viewer', 'member', 'manager', 'admin'],
  admin: ['viewer', 'member', 'manager'],
  manager: ['viewer', 'member'],
  member: [], // Cannot manage roles
  viewer: [], // Cannot manage roles
};

// ============================================================================
// ROUTE PROTECTION
// ============================================================================

/**
 * Maps application routes to required permissions
 *
 * When adding new routes:
 * 1. Add the route path as key
 * 2. Specify the minimum permission required
 * 3. More specific routes should come before general ones
 */
export const routePermissions: Record<string, Permission> = {
  // Device management
  '/devices': Permission.ViewDevices,
  '/edit-device': Permission.ManageDevices,
  '/add-device': Permission.ManageDevices,

  // User profile
  '/profile': Permission.ViewProfile,
  '/profile/edit': Permission.EditProfile,

  // Team management
  '/team': Permission.ViewTeam,
  '/team/invite': Permission.ManageTeam,
  '/team/members': Permission.ViewTeam,

  // Financial
  '/earnings': Permission.ViewEarnings,
  '/earnings/manage': Permission.ManageEarnings,

  // Jobs
  '/jobs': Permission.ViewJobs,
  '/jobs/manage': Permission.ManageJobs,

  // Demo/Test routes
  '/rbac-demo': Permission.ViewDevices,
};

// ============================================================================
// PERMISSION CHECK FUNCTIONS
// ============================================================================

/**
 * Check if a user can change another user's role
 *
 * @param currentUserRole - The role of the user attempting the change
 * @param targetUserRole - The current role of the user being changed
 * @param newRole - The role to assign to the target user
 * @returns Object with canChange boolean and optional reason for denial
 */
export function canChangeUserRole(
  currentUserRole: string,
  targetUserRole: string,
  newRole: string,
): { canChange: boolean; reason?: string } {
  const normalizedCurrentRole = currentUserRole.toLowerCase();
  const normalizedTargetRole = targetUserRole.toLowerCase();
  const normalizedNewRole = newRole.toLowerCase();

  // Get hierarchy levels
  const currentLevel = ROLE_HIERARCHY[normalizedCurrentRole] || 0;
  const targetLevel = ROLE_HIERARCHY[normalizedTargetRole] || 0;
  const newLevel = ROLE_HIERARCHY[normalizedNewRole] || 0;

  // Check management permissions
  const managableRoles = ROLE_MANAGEMENT_PERMISSIONS[normalizedCurrentRole] || [];

  // Validation checks
  if (!managableRoles.includes(normalizedTargetRole)) {
    return {
      canChange: false,
      reason: `You don't have permission to manage users with ${targetUserRole} role`,
    };
  }

  if (!managableRoles.includes(normalizedNewRole)) {
    return {
      canChange: false,
      reason: `You don't have permission to assign ${newRole} role`,
    };
  }

  if (newLevel >= currentLevel) {
    return {
      canChange: false,
      reason: `You cannot promote someone to a role equal or higher than your own`,
    };
  }

  return { canChange: true };
}

/**
 * Check if a user can remove another user from the organization
 *
 * @param currentUserRole - The role of the user attempting removal
 * @param targetUserRole - The role of the user to be removed
 * @returns Object with canRemove boolean and optional reason for denial
 */
export function canRemoveUser(
  currentUserRole: string,
  targetUserRole: string,
): { canRemove: boolean; reason?: string } {
  const normalizedCurrentRole = currentUserRole.toLowerCase();
  const normalizedTargetRole = targetUserRole.toLowerCase();

  // Special rule: Owners cannot be removed
  if (normalizedTargetRole === 'owner') {
    return {
      canRemove: false,
      reason: 'Organization owners cannot be removed',
    };
  }

  // Check management permissions
  const managableRoles = ROLE_MANAGEMENT_PERMISSIONS[normalizedCurrentRole] || [];
  if (!managableRoles.includes(normalizedTargetRole)) {
    return {
      canRemove: false,
      reason: `You don't have permission to remove users with ${targetUserRole} role`,
    };
  }

  return { canRemove: true };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get list of roles that a user can assign to others
 *
 * @param currentUserRole - The role of the user
 * @returns Array of role names that can be assigned
 */
export function getAssignableRoles(currentUserRole: string): string[] {
  const normalizedRole = currentUserRole.toLowerCase();
  return ROLE_MANAGEMENT_PERMISSIONS[normalizedRole] || [];
}

/**
 * Convert external role names to internal permission sets
 *
 * @param externalRoles - Array of role names from external API
 * @returns Array of unique permissions
 */
export function mapRolesToPermissions(externalRoles: string[]): Permission[] {
  const permissions = new Set<Permission>();

  externalRoles.forEach((role) => {
    const rolePermissions = externalRoleToPermissions[role.toLowerCase()];
    if (rolePermissions) {
      rolePermissions.forEach((permission) => permissions.add(permission));
    }
  });

  return Array.from(permissions);
}

/**
 * Check if user has a specific permission
 *
 * @param userPermissions - User's permission array
 * @param requiredPermission - Permission to check for
 * @returns true if user has permission or admin access
 */
export function hasPermission(
  userPermissions: Permission[],
  requiredPermission: Permission,
): boolean {
  return (
    userPermissions.includes(requiredPermission) || userPermissions.includes(Permission.AdminAccess)
  );
}

/**
 * Check if user has any of the required permissions
 *
 * @param userPermissions - User's permission array
 * @param requiredPermissions - Array of permissions (user needs at least one)
 * @returns true if user has any of the required permissions
 */
export function hasAnyPermission(
  userPermissions: Permission[],
  requiredPermissions: Permission[],
): boolean {
  return requiredPermissions.some((permission) => hasPermission(userPermissions, permission));
}

/**
 * Get required permission for a specific route
 *
 * @param pathname - The route path to check
 * @returns Required permission or null if route is unprotected
 */
export function getRoutePermission(pathname: string): Permission | null {
  // Exact match
  if (routePermissions[pathname]) {
    return routePermissions[pathname];
  }

  // Prefix match (for nested routes)
  for (const [route, permission] of Object.entries(routePermissions)) {
    if (pathname.startsWith(route)) {
      return permission;
    }
  }

  return null;
}

// ============================================================================
// HELPER FUNCTIONS FOR UI
// ============================================================================

/**
 * Get human-readable role display name
 */
export function getRoleDisplayName(role: string): string {
  const displayNames: Record<string, string> = {
    viewer: 'Viewer',
    member: 'Member',
    manager: 'Manager',
    admin: 'Administrator',
    superadmin: 'Super Administrator',
    owner: 'Organization Owner',
  };

  return displayNames[role.toLowerCase()] || role;
}

/**
 * Get role description for UI tooltips
 */
export function getRoleDescription(role: string): string {
  const descriptions: Record<string, string> = {
    viewer: 'Can view resources but cannot make changes',
    member: 'Basic user with ability to use devices and edit own profile',
    manager: 'Can manage team members with lower roles',
    admin: 'Full administrative access except ownership transfers',
    superadmin: 'System-wide administration capabilities',
    owner: 'Complete control over the organization',
  };

  return descriptions[role.toLowerCase()] || 'Custom role';
}

/**
 * Check if a role is considered administrative
 */
export function isAdminRole(role: string): boolean {
  const adminRoles = ['admin', 'superadmin', 'owner'];
  return adminRoles.includes(role.toLowerCase());
}
