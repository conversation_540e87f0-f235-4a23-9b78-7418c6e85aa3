// React Query hooks that directly use our internal API gateway routes
// Eliminates the intermediate api-calls layer for cleaner architecture
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { DeviceData, DeviceCardProps } from '@/types/device';
import type { JobsRowProps } from '@/types/jobs';
import type { TeamMember } from '@/types/team';
import type { ActionLogRowProps } from '@/types/logs';
import type { OrgListResponse } from '@/types/team';
import externalClient from '@/app/api/_utils/external-client';
import React from 'react';
import type { UserProfile } from '@/types/user';

/**
 * Enhanced API client that handles React Query invalidations automatically
 */
export const apiClientWithInvalidation = async (
  url: string,
  options?: RequestInit,
  queryClient?: any,
): Promise<any> => {
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  });

  // Check for invalidation headers
  const invalidationHeader = response.headers.get('X-Invalidate-Queries');
  if (invalidationHeader && queryClient) {
    try {
      const invalidationData = JSON.parse(invalidationHeader);
      console.log('🔄 [API-CLIENT] Processing query invalidations:', invalidationData);

      // Invalidate each specified query
      for (const queryKey of invalidationData.queries) {
        await queryClient.invalidateQueries({ queryKey });
        console.log('✅ [API-CLIENT] Invalidated query:', queryKey);
      }
    } catch (error) {
      console.error('❌ [API-CLIENT] Failed to process invalidation header:', error);
    }
  }

  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Request failed' }));
    throw new Error(error.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * API client for making requests to the Next.js API routes
 */
export const apiClient = async (url: string, options?: RequestInit): Promise<any> => {
  const response = await fetch(url, {
    headers: { 'Content-Type': 'application/json' },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API Error: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * Fetch all quantum devices via API gateway
 * Supports filtering by provider, type, status, and availability
 */
export const useAllDevices = (filters?: { provider?: string; type?: string; status?: string }) => {
  const params = new URLSearchParams(filters as Record<string, string>).toString();
  const url = params ? `/api/quantum-devices?${params}` : '/api/quantum-devices';

  return useQuery<DeviceCardProps[]>({
    queryKey: ['devices', filters],
    queryFn: () => apiClient(url),
    enabled: true,
  });
};

/**
 * Fetch a single quantum device by ID via API gateway
 */
export const useDeviceData = (deviceId: string) =>
  useQuery<DeviceData>({
    queryKey: ['device', deviceId],
    queryFn: () => apiClient(`/api/quantum-devices?qbraid_id=${deviceId}`),
    enabled: Boolean(deviceId),
  });

/**
 * Fetch quantum jobs for a specific device and provider via API gateway
 */
export const useJobsForDevice = (
  provider: string,
  device: string,
  page?: number,
  resultsPerPage?: number,
) =>
  useQuery<{ jobsArray: JobsRowProps[]; total: number }>({
    queryKey: ['jobs', provider, device, page, resultsPerPage],
    queryFn: () => {
      const params = new URLSearchParams({
        provider,
        qbraidDeviceId: device,
        page: (page ?? 0).toString(),
        resultsPerPage: (resultsPerPage ?? 10).toString(),
      });
      return apiClient(`/api/quantum-jobs/all-by-provider?${params.toString()}`);
    },
    enabled: Boolean(provider && device),
  });

/**
 * Update quantum device data via API gateway (mutation)
 */
export const useUpdateDeviceData = () => {
  const queryClient = useQueryClient();

  return useMutation<DeviceData, Error, { deviceId: string; postBody: any }>({
    mutationFn: ({ deviceId, postBody }) =>
      apiClient(`/api/quantum-devices/edit?id=${deviceId}`, {
        method: 'PATCH',
        body: JSON.stringify(postBody),
      }),
    onSuccess: (_data, variables) => {
      // Invalidate all device lists (any filters) and the single device cache
      queryClient.invalidateQueries({ queryKey: ['devices'] });
    },
  });
};

/**
 * Fetch organization information via API gateway
 */
export const useOrgInfo = (orgID: string) =>
  useQuery({
    queryKey: ['org', orgID],
    queryFn: () => apiClient(`/api/orgs/get/${orgID}`),
    enabled: Boolean(orgID),
  });

/**
 * Fetch users in an organization via API gateway
 * Uses the consolidated orgs/users endpoint with enhanced error handling
 */
export const useOrgUsers = (orgID: string, page: number, pageSize: number) => {
  console.log('🔍 [USE-ORG-USERS] Hook called:', {
    orgID,
    page,
    pageSize,
    timestamp: new Date().toISOString(),
  });

  const query = useQuery<{
    users: TeamMember[];
    totalUsers: number;
    currentUserRole?: string | null;
    message?: string;
  }>({
    queryKey: ['orgUsers', orgID, page, pageSize],
    queryFn: async () => {
      const startTime = Date.now();
      console.log('📡 [USE-ORG-USERS] API call starting:', {
        orgID,
        page,
        pageSize,
        endpoint: `/api/orgs/users/${orgID}/${page}/${pageSize}`,
      });

      try {
        const result = await apiClient(`/api/orgs/users/${orgID}/${page}/${pageSize}`);
        const duration = Date.now() - startTime;

        console.log('✅ [USE-ORG-USERS] API call successful:', {
          orgID,
          duration: duration + 'ms',
          usersCount: result?.users?.length || 0,
          totalUsers: result?.totalUsers || 0,
          hasUsers: !!result?.users,
          dataSize: JSON.stringify(result).length + ' bytes',
          currentUserRole: result?.currentUserRole,
        });

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        console.error('❌ [USE-ORG-USERS] API call failed:', {
          orgID,
          duration: duration + 'ms',
          errorMessage: error instanceof Error ? error.message : String(error),
          errorType: error instanceof Error ? error.name : 'Unknown',
        });
        throw error;
      }
    },
    enabled: Boolean(orgID),
  });

  // Log success/error states using useEffect
  React.useEffect(() => {
    if (query.isSuccess && query.data) {
      console.log('🎉 [USE-ORG-USERS] Query success:', {
        orgID,
        usersReceived: query.data?.users?.length || 0,
        totalUsers: query.data?.totalUsers || 0,
        currentUserRole: query.data?.currentUserRole,
      });
    }
  }, [query.isSuccess, query.data, orgID]);

  React.useEffect(() => {
    if (query.isError && query.error) {
      console.error('🚨 [USE-ORG-USERS] Query error:', {
        orgID,
        error: query.error instanceof Error ? query.error.message : String(query.error),
      });
    }
  }, [query.isError, query.error, orgID]);

  return query;
};

/**
 * Enhanced team member manipulation hooks using new team API endpoints
 */

/**
 * Invite user to organization via API gateway
 * Uses the consolidated orgs/users/add endpoint
 */
export const useInviteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: { email: string; orgId: string; role: string; orgName: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/add',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
  });
};

/**
 * Update user role in organization
 * Uses the consolidated orgs/users/update endpoint
 */
export const useUpdateUserRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: {
      email: string;
      orgId: string;
      role: string;
      orgName: string;
      accepted?: boolean;
      credits?: number;
    }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/update',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
  });
};

/**
 * Remove user from organization
 * Uses the consolidated orgs/users/remove endpoint
 */
export const useRemoveUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: { email: string; orgId: string; orgName: string; reason?: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/remove',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
  });
};

/**
 * Accept organization invitation
 * Uses the orgs/users/accept endpoint
 */
export const useAcceptInvitation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: { orgId: string; email: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/accept',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
  });
};

/**
 * Decline organization invitation
 * Uses the orgs/users/remove endpoint with decline reason
 */
export const useDeclineInvitation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: { orgName: string; email: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/remove',
        {
          method: 'POST',
          body: JSON.stringify({
            ...body,
            reason: 'decline',
          }),
        },
        queryClient,
      ),
  });
};

/**
 * Legacy hook for backward compatibility - now uses new team endpoints
 * @deprecated Use useInviteUser, useUpdateUserRole, or useRemoveUser instead
 */
export const useManipulateUsers = () => {
  console.log('⚠️ [USE-MANIPULATE-USERS] Deprecated hook called at:', new Date().toISOString());

  return useMutation({
    mutationFn: async ({ action, body }: { action: string; body: any }) => {
      const startTime = Date.now();
      const requestId = Math.random().toString(36).substring(2, 15);

      console.log('📡 [USE-MANIPULATE-USERS] Mutation starting:', {
        requestId,
        action,
        timestamp: new Date().toISOString(),
        bodyType: typeof body,
        bodySize: JSON.stringify(body).length + ' bytes',
      });

      let endpoint: string;
      let parsedBody: any;

      try {
        parsedBody = typeof body === 'string' ? JSON.parse(body) : body;
        console.log('🔧 [USE-MANIPULATE-USERS] Body parsed:', {
          requestId,
          originalType: typeof body,
          parsedKeys: Object.keys(parsedBody),
          hasEmail: !!parsedBody.email,
          hasRole: !!parsedBody.role,
        });
      } catch (parseError) {
        console.error('❌ [USE-MANIPULATE-USERS] Body parse failed:', {
          requestId,
          error: parseError instanceof Error ? parseError.message : String(parseError),
        });
        parsedBody = body;
      }

      switch (action) {
        case 'invite':
          endpoint = '/api/team/members/invite';
          console.log('👥 [USE-MANIPULATE-USERS] Invite action:', {
            requestId,
            email: parsedBody.email,
            role: parsedBody.role,
          });
          break;
        case 'change-role':
          endpoint = '/api/team/members/update-role';
          // Map legacy field names to new API
          if (parsedBody.name) {
            parsedBody.orgName = parsedBody.name;
            delete parsedBody.name;
            console.log('🔄 [USE-MANIPULATE-USERS] Mapped legacy field name to orgName:', {
              requestId,
              orgName: parsedBody.orgName,
            });
          }
          console.log('🔄 [USE-MANIPULATE-USERS] Change role action:', {
            requestId,
            email: parsedBody.email,
            newRole: parsedBody.role,
          });
          break;
        case 'remove':
          endpoint = '/api/team/members/remove';
          // Map legacy field names to new API
          if (parsedBody.name) {
            parsedBody.orgName = parsedBody.name;
            delete parsedBody.name;
            console.log('🗑️ [USE-MANIPULATE-USERS] Mapped legacy field name to orgName:', {
              requestId,
              orgName: parsedBody.orgName,
            });
          }
          console.log('🗑️ [USE-MANIPULATE-USERS] Remove action:', {
            requestId,
            email: parsedBody.email,
          });
          break;
        case 'resend-invite':
          // For now, use invite endpoint for resend functionality
          endpoint = '/api/team/members/invite';
          parsedBody.resend = true;
          console.log('📧 [USE-MANIPULATE-USERS] Resend invite action:', {
            requestId,
            email: parsedBody.email,
            isResend: true,
          });
          break;
        default:
          console.error('❌ [USE-MANIPULATE-USERS] Invalid action:', {
            requestId,
            action,
            validActions: ['invite', 'change-role', 'remove', 'resend-invite'],
          });
          throw new Error(`Invalid action: ${action}`);
      }

      try {
        const result = await apiClient(endpoint, {
          method: 'POST',
          body: JSON.stringify(parsedBody),
        });

        const duration = Date.now() - startTime;
        console.log('✅ [USE-MANIPULATE-USERS] Mutation successful:', {
          requestId,
          action,
          endpoint,
          duration: duration + 'ms',
          responseSize: JSON.stringify(result).length + ' bytes',
          success: result?.success !== false,
        });

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        console.error('❌ [USE-MANIPULATE-USERS] Mutation failed:', {
          requestId,
          action,
          endpoint,
          duration: duration + 'ms',
          errorMessage: error instanceof Error ? error.message : String(error),
          errorType: error instanceof Error ? error.name : 'Unknown',
        });
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log('🎉 [USE-MANIPULATE-USERS] Mutation success callback:', {
        action: variables.action,
        success: data?.success !== false,
        message: data?.message,
      });
    },
    onError: (error, variables) => {
      console.error('🚨 [USE-MANIPULATE-USERS] Mutation error callback:', {
        action: variables.action,
        error: error instanceof Error ? error.message : String(error),
      });
    },
  });
};

/**
 * Fetch audit logs via new team activity logs API gateway
 * Uses the new activity logs endpoint with pagination support
 */
export const useActionLogs = (provider: string, page: number, resultsPerPage: number) => {
  console.log('🔍 [USE-ACTION-LOGS] Hook called:', {
    provider,
    page,
    resultsPerPage,
    timestamp: new Date().toISOString(),
  });

  const query = useQuery<{ auditLogsArray: ActionLogRowProps[] }>({
    queryKey: ['actionLogs', provider, page, resultsPerPage],
    queryFn: async () => {
      const startTime = Date.now();
      console.log('📡 [USE-ACTION-LOGS] API call starting:', {
        provider,
        page,
        resultsPerPage,
        endpoint: `/api/team/activity-logs/${provider}/${page}/${resultsPerPage}`,
      });

      try {
        const result = await apiClient(
          `/api/team/activity-logs/${provider}/${page}/${resultsPerPage}`,
        );
        const duration = Date.now() - startTime;

        console.log('✅ [USE-ACTION-LOGS] API call successful:', {
          provider,
          duration: duration + 'ms',
          logsCount: result?.auditLogsArray?.length || 0,
          hasLogs: !!result?.auditLogsArray,
          dataSize: JSON.stringify(result).length + ' bytes',
        });

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        console.error('❌ [USE-ACTION-LOGS] API call failed:', {
          provider,
          duration: duration + 'ms',
          errorMessage: error instanceof Error ? error.message : String(error),
          errorType: error instanceof Error ? error.name : 'Unknown',
        });
        throw error;
      }
    },
    enabled: Boolean(provider),
  });

  // Log success/error states using useEffect
  React.useEffect(() => {
    if (query.isSuccess && query.data) {
      console.log('🎉 [USE-ACTION-LOGS] Query success:', {
        provider,
        logsReceived: query.data?.auditLogsArray?.length || 0,
      });
    }
  }, [query.isSuccess, query.data, provider]);

  React.useEffect(() => {
    if (query.isError && query.error) {
      console.error('🚨 [USE-ACTION-LOGS] Query error:', {
        provider,
        error: query.error instanceof Error ? query.error.message : String(query.error),
      });
    }
  }, [query.isError, query.error, provider]);

  return query;
};

/**
 * Submit an audit log entry via new team activity logs API gateway
 */
export const useSubmitActionLog = () =>
  useMutation({
    mutationFn: (params: {
      provider: string;
      email: string;
      role: string;
      type: string;
      deviceId?: string;
      otherUserEmail?: string;
      otherUserRole?: string;
      description?: string;
      metadata?: Record<string, any>;
    }) =>
      apiClient('/api/team/activity-logs/submit', {
        method: 'POST',
        body: JSON.stringify(params),
      }),
  });

/**
 * Get current user's profile information
 * Uses the external API /get-user-info endpoint
 */
export const useUserProfile = () => {
  return useQuery<UserProfile>({
    queryKey: ['userProfile'],
    queryFn: async () => {
      const response = await fetch('/api/user/profile');
      if (!response.ok) {
        throw new Error('Failed to fetch user profile');
      }
      const data = await response.json();
      // Extract the profile from the API response
      return data.profile || data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Get current user's organizations
 * Uses the external API /orgs/get/:page/:limit endpoint
 */
export const useUserOrganizations = (page: number = 0, limit: number = 10) => {
  return useQuery({
    queryKey: ['userOrganizations', page, limit],
    queryFn: async () => {
      const response = await fetch(`/api/user/organizations/${page}/${limit}`);
      if (!response.ok) {
        throw new Error('Failed to fetch user organizations');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Update user profile
 * Uses the external API to update user information
 */
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: Partial<UserProfile>) =>
      apiClientWithInvalidation(
        '/api/user/profile',
        {
          method: 'PUT',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
    onSuccess: () => {
      // Invalidate profile cache after successful update
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
    },
  });
};

/**
 * Edit quantum device
 */
export const useEditDevice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ deviceId, ...body }: { deviceId: string; [key: string]: any }) =>
      apiClientWithInvalidation(
        `/api/quantum-devices/edit?id=${deviceId}`,
        {
          method: 'PATCH',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
  });
};

/**
 * Submit activity log
 */
export const useSubmitActivityLog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: {
      provider: string;
      email: string;
      role: string;
      type: string;
      deviceId?: string;
      otherUserEmail?: string;
      otherUserRole?: string;
      description?: string;
      metadata?: Record<string, any>;
    }) =>
      apiClientWithInvalidation(
        '/api/team/activity-logs/submit',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
  });
};

/**
 * Update user role in organization (admin endpoint)
 */
export const useUpdateOrgRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: { targetEmail: string; orgId: string; newRole: string; orgName?: string }) =>
      apiClientWithInvalidation(
        '/api/user/org-roles',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
  });
};

/**
 * Fetch the list of organisations the user belongs to (paginated) together
 * with their role inside each organisation. Uses QBraid API directly.
 */
export const useOrgList = (page: number, limit: number) =>
  useQuery<OrgListResponse>({
    queryKey: ['orgList', page, limit],
    queryFn: async () => {
      const res = await externalClient.get(`/orgs/get/${page}/${limit}`);
      return res.data;
    },
  });

/**
 * SIMPLE ROLE FETCHING - Direct QBraid API calls via Next.js gateway
 * Much simpler than the complex multi-step role fetching system
 */

/**
 * Get user role in a specific organization
 * Single API call to QBraid API via Next.js gateway
 */
export const useUserRole = (orgId: string, enabled: boolean = true) =>
  useQuery<{
    success: boolean;
    data: {
      email: string;
      orgId: string;
      role: string | null;
      currentUserRole?: string;
      orgUsers: any[];
      pagination: any;
    };
    processingTime: string;
  }>({
    queryKey: ['userRole', orgId],
    queryFn: () => apiClient(`/api/user/roles?orgId=${orgId}&page=0&limit=100`),
    enabled: enabled && !!orgId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

/**
 * Get user roles across multiple organizations
 * Simple parallel API calls via Next.js gateway
 */
export const useBulkUserRoles = () =>
  useMutation<
    {
      success: boolean;
      data: {
        email: string;
        roles: string[];
        orgRoles: Array<{
          orgId: string;
          role: string | null;
          currentUserRole?: string;
          error?: string;
        }>;
        totalOrgsChecked: number;
        rolesFound: number;
      };
      processingTime: string;
    },
    Error,
    string[]
  >({
    mutationFn: (orgIds: string[]) =>
      apiClient('/api/user/roles', {
        method: 'POST',
        body: JSON.stringify({ orgIds }),
      }),
  });
