# Teams Page Comprehensive Guide

## Table of Contents

1. [Overview](#overview)
2. [User Management](#user-management)
3. [Role Management](#role-management)
4. [Permissions System](#permissions-system)
5. [API Integration](#api-integration)
6. [UI Components](#ui-components)
7. [Security & Validation](#security--validation)
8. [Troubleshooting](#troubleshooting)

## Overview

The Teams page (`/team`) is a comprehensive team management interface that allows organization administrators to manage team members, roles, and permissions. It provides functionality for inviting users, changing roles, removing users, and monitoring team activity.

### Key Features

- **User Invitation**: Single, multiple, and bulk user invitation with role assignment
- **Role Management**: Hierarchical role system with permission-based access control
- **User Removal**: Secure user removal with confirmation dialogs
- **Activity Monitoring**: Audit logs and team activity tracking
- **Search & Filtering**: Advanced search and role-based filtering
- **Pagination**: Efficient handling of large team datasets

### Page Structure

```
/team
├── Team Members Tab (default)
├── Roles Tab
└── Activity Log Tab
```

## User Management

### Adding Users

#### 1. Single User Invitation

**Access**: Users with `ManageTeam` permission (Manager, Admin, Owner)

**Process**:

1. Click "Invite Member" button
2. Select "Single Invite" tab
3. Enter email address
4. Select appropriate role
5. Click "Send Invitation"

**Validation**:

- Valid email format required
- Cannot invite yourself
- Role must be selected from available options
- User must have permission to assign the selected role

#### 2. Multiple User Invitation

**Process**:

1. Click "Invite Member" button
2. Select "Multiple Invites" tab (default)
3. Add multiple email/role combinations
4. Use "Add Another" to include more users
5. Click "Send Invitations"

**Features**:

- Dynamic form fields
- Individual role selection per user
- Remove individual entries
- Bulk validation

#### 3. Bulk User Invitation

**Process**:

1. Click "Invite Member" button
2. Select "Bulk Invite" tab
3. Enter multiple emails (comma, space, or newline separated)
4. Select single role for all users
5. Click "Send Invitations"

**Email Parsing**:

- Supports comma separation: `<EMAIL>, <EMAIL>`
- Supports space separation: `<EMAIL> <EMAIL>`
- Supports newline separation
- Automatic email validation and deduplication

### Changing User Roles

#### Permission Requirements

Role changes follow a strict hierarchy system:

```typescript
ROLE_HIERARCHY = {
  viewer: 1, // Read-only access
  member: 2, // Basic user access
  manager: 3, // Team management
  admin: 4, // Full administrative access
  superadmin: 5, // System-wide administration
  owner: 6, // Organization owner
};
```

#### Role Change Rules

- Users can only manage roles below their hierarchy level
- Cannot promote users to or above your own role level
- Owners cannot be managed by anyone except other owners
- Members and viewers cannot manage any roles

#### Process

1. Click "Change Role" button next to user
2. Select new role from dropdown (filtered by permissions)
3. Review role permissions and description
4. Confirm the change
5. System validates permissions and applies change

### Removing Users

#### Permission Requirements

- Must have `ManageTeam` permission
- Can only remove users with roles below your hierarchy level
- Cannot remove yourself
- Cannot remove organization owners (unless you're an owner)

#### Process

1. Click "Remove" button next to user
2. Confirm removal in dialog
3. User is immediately removed from organization
4. All user access is revoked
5. Audit log entry is created

**Security Features**:

- Double confirmation required
- Clear warning about irreversible action
- Immediate session invalidation for removed user
- Automatic cleanup of user permissions

## Role Management

### Available Roles

#### Owner

- **Hierarchy Level**: 6 (Highest)
- **Permissions**: Full control over organization
- **Capabilities**:
  - Manage all users and roles
  - Delete organization
  - Transfer ownership
  - Billing and payment management
  - All admin capabilities

#### Admin

- **Hierarchy Level**: 4
- **Permissions**: Full administrative access
- **Capabilities**:
  - Manage team members (except owners)
  - Configure organization settings
  - View all data and analytics
  - Cannot delete organization

#### Manager

- **Hierarchy Level**: 3
- **Permissions**: Team management capabilities
- **Capabilities**:
  - Invite and manage members and viewers
  - View team analytics
  - Manage team resources
  - Cannot manage admins or owners

#### Member

- **Hierarchy Level**: 2
- **Permissions**: Standard user access
- **Capabilities**:
  - Use devices and submit jobs
  - View own data and analytics
  - Collaborate with team
  - Edit own profile

#### Viewer

- **Hierarchy Level**: 1
- **Permissions**: Read-only access
- **Capabilities**:
  - View resources and analytics
  - Read team information
  - Cannot modify any data
  - Cannot submit jobs

### Role Assignment Rules

```typescript
ROLE_MANAGEMENT_PERMISSIONS = {
  owner: ['viewer', 'member', 'manager', 'admin', 'superadmin'],
  superadmin: ['viewer', 'member', 'manager', 'admin'],
  admin: ['viewer', 'member', 'manager'],
  manager: ['viewer', 'member'],
  member: [], // Cannot manage roles
  viewer: [], // Cannot manage roles
};
```

## Permissions System

### Permission Types

The system uses granular permissions mapped to roles:

```typescript
enum Permission {
  // Device Management
  ViewDevices = 'view:devices',
  ManageDevices = 'manage:devices',

  // Team Management
  ViewTeam = 'view:team',
  ManageTeam = 'manage:team',

  // Profile Management
  ViewProfile = 'view:profile',
  EditProfile = 'edit:profile',

  // Administrative
  AdminAccess = 'admin:access',

  // Financial
  ViewEarnings = 'view:earnings',
  ManageEarnings = 'manage:earnings',
}
```

### Permission Checking

#### Client-Side

```typescript
import { usePermissions } from '@/hooks/use-permissions';

const { hasPermission, role } = usePermissions();

// Check specific permission
if (hasPermission(Permission.ManageTeam)) {
  // Show team management UI
}

// Check role
if (role === 'admin') {
  // Show admin features
}
```

#### Server-Side

```typescript
import { requirePermission } from '@/lib/rbac';

// In API routes
await requirePermission(Permission.ManageTeam);

// In server actions
await requirePermission(Permission.ManageTeam, '/unauthorized');
```

### Route Protection

Routes are automatically protected based on permission requirements:

```typescript
routePermissions = {
  '/team': Permission.ViewTeam,
  '/team/invite': Permission.ManageTeam,
  '/team/members': Permission.ViewTeam,
};
```

## API Integration

### Endpoints

#### Invite User

```typescript
POST /api/orgs/users/add
{
  "email": "<EMAIL>",
  "role": "member",
  "orgId": "org_123",
  "orgName": "My Organization"
}
```

#### Update User Role

```typescript
POST /api/orgs/users/update
{
  "email": "<EMAIL>",
  "role": "admin",
  "orgId": "org_123",
  "orgName": "My Organization"
}
```

#### Remove User

```typescript
POST /api/orgs/users/remove
{
  "email": "<EMAIL>",
  "orgId": "org_123",
  "orgName": "My Organization",
  "reason": "voluntary_leave"
}
```

### React Query Hooks

#### useInviteUser

```typescript
const { mutate: inviteUser, isPending } = useInviteUser();

inviteUser({
  email: '<EMAIL>',
  role: 'member',
  orgId: currentOrgId,
  orgName: currentOrg.orgName,
});
```

#### useUpdateUserRole

```typescript
const { mutate: updateRole } = useUpdateUserRole();

updateRole({
  email: '<EMAIL>',
  role: 'admin',
  orgId: currentOrgId,
  orgName: currentOrg.orgName,
});
```

#### useRemoveUser

```typescript
const { mutate: removeUser } = useRemoveUser();

removeUser({
  email: '<EMAIL>',
  orgId: currentOrgId,
  orgName: currentOrg.orgName,
});
```

### Cache Invalidation

The system automatically invalidates relevant React Query caches:

```typescript
// After user operations
queries: [
  ['orgUsers', orgId], // Refresh user list
  ['userOrgRole', email, orgId], // Refresh user role
  ['permissions'], // Refresh permissions
  ['userOrganizations'], // Refresh org list
];
```

## UI Components

### Main Components

#### TeamMembersTab

- User list with search and filtering
- Pagination controls
- Role change and remove actions
- Loading states and error handling

#### InviteModal

- Three invitation modes (single, multiple, bulk)
- Role selection with descriptions
- Form validation and error handling
- Success/error feedback

#### ChangeRoleButton

- Permission-aware role selection
- Hierarchy validation
- Confirmation dialog
- Loading states

#### RemoveUserButton

- Confirmation dialog with warnings
- Success/error feedback
- Immediate UI updates

### Search and Filtering

#### Search Functionality

- Real-time search across name and email
- Debounced input (300ms delay)
- Case-insensitive matching
- Highlights matching terms

#### Role Filtering

- Filter by specific roles
- "All Roles" option
- Dynamic role list based on organization
- Maintains search state during filtering

#### Pagination

- Configurable page sizes (10, 25, 50, 100)
- Previous/Next navigation
- Page size persistence
- Total count display

## Security & Validation

### Input Validation

#### Email Validation

- RFC-compliant email format
- Domain validation
- Duplicate prevention
- Sanitization of input

#### Role Validation

- Server-side role existence check
- Permission hierarchy validation
- Assignment permission verification
- Role transition rules enforcement

### Security Measures

#### Authentication

- Session-based authentication required
- Token validation on each request
- Automatic session refresh
- Secure logout handling

#### Authorization

- Role-based access control (RBAC)
- Permission-based UI rendering
- Server-side permission validation
- Audit trail for all actions

#### Data Protection

- Input sanitization
- SQL injection prevention
- XSS protection
- CSRF token validation

### Error Handling

#### Client-Side

- Form validation errors
- Network error handling
- Loading state management
- User-friendly error messages

#### Server-Side

- Comprehensive error logging
- Structured error responses
- Rate limiting protection
- Graceful degradation

## Troubleshooting

### Common Issues

#### "Permission Denied" Errors

**Cause**: User lacks required permissions
**Solution**:

1. Check user's current role
2. Verify role hierarchy
3. Ensure user has `ManageTeam` permission
4. Contact organization admin if needed

#### Invitation Emails Not Sent

**Cause**: Email service configuration or invalid email
**Solution**:

1. Verify email address format
2. Check spam/junk folders
3. Confirm email service is operational
4. Retry invitation after 5 minutes

#### Role Changes Not Reflecting

**Cause**: Cache invalidation issues
**Solution**:

1. Refresh the page
2. Clear browser cache
3. Check network connectivity
4. Verify API response in developer tools

#### Users Not Appearing in List

**Cause**: Pagination or filtering issues
**Solution**:

1. Clear search filters
2. Check different pages
3. Verify user is in correct organization
4. Refresh user list

### Debug Information

#### Logging

- All team operations are logged with request IDs
- Check browser console for client-side errors
- Server logs include detailed error context
- Audit logs track all user management actions

#### Performance Monitoring

- API response times tracked
- Query performance metrics
- Cache hit/miss ratios
- User interaction analytics

### Support Contacts

For technical issues:

1. Check application logs
2. Review error messages
3. Contact system administrator
4. Submit support ticket with request ID

## Advanced Features

### Bulk Operations

#### Bulk Role Changes

While not currently implemented in the UI, the system supports bulk role changes through API:

```typescript
// Future implementation
const bulkUpdateRoles = async (updates: Array<{ email: string; newRole: string }>) => {
  for (const update of updates) {
    await updateUserRole({
      email: update.email,
      role: update.newRole,
      orgId: currentOrgId,
      orgName: currentOrg.orgName,
    });
  }
};
```

#### Bulk User Removal

```typescript
// Future implementation
const bulkRemoveUsers = async (emails: string[]) => {
  for (const email of emails) {
    await removeUser({
      email,
      orgId: currentOrgId,
      orgName: currentOrg.orgName,
      reason: 'bulk_removal',
    });
  }
};
```

### Activity Monitoring

#### Audit Logs

The system maintains comprehensive audit logs for all team operations:

```typescript
// Log entry structure
{
  timestamp: "2025-01-14T10:30:00Z",
  action: "user_invited" | "role_changed" | "user_removed",
  actor: "<EMAIL>",
  target: "<EMAIL>",
  details: {
    oldRole?: "member",
    newRole?: "admin",
    orgId: "org_123",
    orgName: "My Organization"
  },
  metadata: {
    userAgent: "...",
    ipAddress: "***********",
    requestId: "req_abc123"
  }
}
```

#### Real-time Updates

- WebSocket connections for live updates
- Automatic refresh when team changes occur
- Push notifications for important events
- Conflict resolution for concurrent edits

### Integration Points

#### External Identity Providers

The system can integrate with external identity providers:

- **SAML SSO**: Enterprise single sign-on
- **OAuth Providers**: Google, Microsoft, GitHub
- **LDAP/Active Directory**: Enterprise directory services
- **Custom Identity Providers**: API-based integration

#### Webhook Integration

```typescript
// Webhook payload for team events
{
  event: "team.member.added",
  timestamp: "2025-01-14T10:30:00Z",
  organization: {
    id: "org_123",
    name: "My Organization"
  },
  data: {
    user: {
      email: "<EMAIL>",
      role: "member",
      invitedBy: "<EMAIL>"
    }
  }
}
```

### Performance Optimization

#### Caching Strategy

- **React Query**: Client-side caching with automatic invalidation
- **Redis**: Server-side session and permission caching
- **CDN**: Static asset caching
- **Database**: Query optimization and indexing

#### Lazy Loading

- User list virtualization for large teams
- Progressive loading of user details
- Image lazy loading for avatars
- Component code splitting

#### Search Optimization

- Debounced search input (300ms)
- Server-side search indexing
- Fuzzy search capabilities
- Search result caching

## Best Practices

### User Management

#### Invitation Best Practices

1. **Role Assignment**: Start with minimal permissions and escalate as needed
2. **Bulk Invitations**: Use for onboarding large teams efficiently
3. **Email Verification**: Always verify email addresses before sending invitations
4. **Documentation**: Maintain records of why users were invited and their intended role

#### Role Management Best Practices

1. **Principle of Least Privilege**: Assign minimum necessary permissions
2. **Regular Audits**: Review user roles quarterly
3. **Role Transitions**: Document role changes and reasons
4. **Emergency Access**: Maintain emergency admin access procedures

#### Security Best Practices

1. **Regular Reviews**: Audit team membership monthly
2. **Offboarding**: Remove users immediately upon departure
3. **Access Monitoring**: Monitor unusual access patterns
4. **Backup Admins**: Maintain multiple admin users

### Development Guidelines

#### Component Development

```typescript
// Example: Creating a new team management component
import { usePermissions } from '@/hooks/use-permissions';
import { Permission } from '@/types/auth';

export function TeamManagementComponent() {
  const { hasPermission, role } = usePermissions();

  // Always check permissions before rendering
  if (!hasPermission(Permission.ViewTeam)) {
    return <UnauthorizedMessage />;
  }

  return (
    <div>
      {/* Team content */}
      {hasPermission(Permission.ManageTeam) && (
        <AdminControls />
      )}
    </div>
  );
}
```

#### API Development

```typescript
// Example: Creating a new team API endpoint
import { requirePermission } from '@/lib/rbac';
import { Permission } from '@/types/auth';

export async function POST(request: NextRequest) {
  // Always validate permissions first
  await requirePermission(Permission.ManageTeam);

  // Validate input
  const body = await request.json();
  const validatedData = schema.parse(body);

  // Perform operation
  const result = await performTeamOperation(validatedData);

  // Return response with cache invalidation headers
  const response = NextResponse.json(result);
  response.headers.set(
    'X-Invalidate-Queries',
    JSON.stringify({
      queries: [['orgUsers', orgId]],
      message: 'Team updated',
    }),
  );

  return response;
}
```

### Testing Guidelines

#### Unit Testing

```typescript
// Example: Testing team management functions
describe('Team Management', () => {
  it('should allow admin to invite users', async () => {
    const mockAdmin = { role: 'admin', permissions: [Permission.ManageTeam] };
    const result = await inviteUser(mockAdmin, '<EMAIL>', 'member');
    expect(result.success).toBe(true);
  });

  it('should prevent member from changing roles', async () => {
    const mockMember = { role: 'member', permissions: [Permission.ViewTeam] };
    await expect(changeUserRole(mockMember, '<EMAIL>', 'admin')).rejects.toThrow(
      'Permission denied',
    );
  });
});
```

#### Integration Testing

```typescript
// Example: Testing team API endpoints
describe('Team API', () => {
  it('should handle user invitation flow', async () => {
    // Setup test organization and admin user
    const { org, admin } = await setupTestOrg();

    // Test invitation
    const response = await request(app)
      .post('/api/orgs/users/add')
      .set('Authorization', `Bearer ${admin.token}`)
      .send({
        email: '<EMAIL>',
        role: 'member',
        orgId: org.id,
        orgName: org.name,
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});
```

## Migration Guide

### From Legacy System

If migrating from an older team management system:

#### Data Migration

1. **Export existing user data**: roles, permissions, and metadata
2. **Map legacy roles** to new role hierarchy
3. **Validate email addresses** and clean invalid entries
4. **Import users** using bulk invitation API
5. **Verify permissions** after migration

#### Code Migration

1. **Update permission checks** to use new Permission enum
2. **Replace legacy hooks** with new usePermissions hook
3. **Update API calls** to use new endpoints
4. **Test role-based access** thoroughly

### Version Compatibility

#### Breaking Changes

- **v1.0**: Initial implementation
- **v1.1**: Added bulk operations (backward compatible)
- **v2.0**: New permission system (requires migration)

#### Upgrade Path

1. **Backup current data** before upgrading
2. **Run migration scripts** for permission system
3. **Update client code** to use new APIs
4. **Test all functionality** in staging environment
5. **Deploy to production** with rollback plan

---

_Last updated: 2025-01-14_
_Version: 1.0_
