# Teams Page Complete Developer Guide

> **Open Source Senior Developer Documentation**  
> A comprehensive guide for implementing, maintaining, and extending the Teams page functionality in a Next.js 14+ application with enterprise-grade RBAC, performance optimization, and production-ready patterns.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Architecture Overview](#architecture-overview)
3. [User Management Operations](#user-management-operations)
4. [Role-Based Access Control (RBAC)](#role-based-access-control-rbac)
5. [API Integration & Hooks](#api-integration--hooks)
6. [Component Architecture](#component-architecture)
7. [State Management Patterns](#state-management-patterns)
8. [Workflows & Data Flow](#workflows--data-flow)
9. [Performance & Optimization](#performance--optimization)
10. [Security Implementation](#security-implementation)
11. [Testing Strategy](#testing-strategy)
12. [Troubleshooting & Best Practices](#troubleshooting--best-practices)

---

## Quick Start

### Essential Operations

#### Adding Users

```bash
# Single User
1. Click "Invite Member" → Enter email & role → Send Invitation

# Multiple Users
2. Click "Invite Member" → "Multiple Invites" tab → Add pairs → Send Invitations

# Bulk Users
3. Click "Invite Member" → "Bulk Invite" tab → Paste emails → Select role → Send
```

#### Managing Roles

```bash
# Change Role: Find user → "Change Role" → Select new role → Confirm
# Remove User: Find user → "Remove" → Confirm (⚠️ Irreversible)
```

### Role Hierarchy Quick Reference

| Role           | Level | Can Manage                     | Key Permissions                   |
| -------------- | ----- | ------------------------------ | --------------------------------- |
| **Owner**      | 6     | All roles                      | Full control, billing, delete org |
| **SuperAdmin** | 5     | Admin, Manager, Member, Viewer | System administration             |
| **Admin**      | 4     | Manager, Member, Viewer        | Team management, settings         |
| **Manager**    | 3     | Member, Viewer                 | Team oversight, basic admin       |
| **Member**     | 2     | None                           | Standard user access              |
| **Viewer**     | 1     | None                           | Read-only access                  |

---

## Architecture Overview

### Technology Stack

```typescript
// Core Technologies
Framework: Next.js 14+ (App Router + Server Components)
Language: TypeScript 5.0+ (strict mode)
Styling: Tailwind CSS + Custom Design System
State: TanStack Query v5 + React useState/useReducer
Forms: React Hook Form + Zod validation
UI: Radix UI + Custom Components
Auth: Custom session management + Redis
Cache: React Query + Redis server-side
Icons: Lucide React
```

### File Structure

```
app/(dashboard)/team/
├── page.tsx                    # Main team page container
components/team/
├── team-stats.tsx             # Statistics dashboard
├── team-members-tab.tsx       # Team members management
├── team-member-row.tsx        # Individual member component
├── roles-tab.tsx              # Roles and permissions display
├── activity-log-tab.tsx       # Activity monitoring
├── invite-modal.tsx           # Multi-modal user invitation
├── change-role-button.tsx     # Role modification component
├── remove-user-button.tsx     # User removal with confirmation
└── pagination-controls.tsx    # Reusable pagination
hooks/
├── use-api.ts                 # API integration hooks
├── use-permissions.tsx        # Permission management
├── use-debounce.ts           # Search debouncing utility
└── use-pagination.ts         # Pagination state logic
lib/
├── permissions.ts            # RBAC core implementation
├── rbac.ts                   # Server-side RBAC utilities
└── session.ts               # Session management
types/
├── auth.ts                   # Authentication types
├── team.ts                   # Team-related interfaces
└── api.ts                    # API response types
```

### Component Hierarchy

```
TeamPage (app/(dashboard)/team/page.tsx)
├── OrgPermissionGuard (Permission.ViewTeam)
├── TeamStats
├── Tabs Navigation
├── TeamMembersTab
│   ├── Search & Filter Controls
│   ├── TeamMemberRow[] (with actions)
│   └── PaginationControls
├── RolesTab
│   └── Role Permission Cards
├── ActivityLogTab
│   ├── Activity Search & Filter
│   └── Activity Entries with Pagination
└── Modal System
    ├── InviteModal (Single/Multiple/Bulk modes)
    ├── ChangeRoleButton Modal
    └── RemoveUserButton Confirmation
```

---

## User Management Operations

### 1. User Invitation System

#### Single User Invitation

```typescript
// Implementation pattern
const handleSingleInvite = async (email: string, role: string) => {
  try {
    await inviteUser({
      email,
      role,
      orgId: currentOrgId,
      orgName: currentOrg?.orgName || '',
    });

    // Success handling
    setActionDialogMessage(`Successfully invited ${email}`);
    refetchUsers(); // Refresh team list
  } catch (error) {
    // Error handling with user feedback
    setActionDialogMessage(`Failed to invite user: ${error.message}`);
  }
};
```

#### Multiple User Invitation

```typescript
// Dynamic form handling
const multipleForm = useForm<MultipleInviteForm>({
  resolver: zodResolver(multipleInviteSchema),
  defaultValues: {
    invites: [{ email: '', role: '' }],
  },
});

// Add/remove invite fields dynamically
const addInviteField = () => {
  const currentInvites = multipleForm.getValues('invites');
  multipleForm.setValue('invites', [...currentInvites, { email: '', role: '' }]);
};
```

#### Bulk User Invitation

```typescript
// Email parsing utility
const parseBulkEmails = (emailString: string): string[] => {
  return emailString
    .split(/[,\s\n]+/)
    .map((email) => email.trim())
    .filter((email) => email.length > 0)
    .filter((email, index, arr) => arr.indexOf(email) === index); // Deduplicate
};

// Bulk processing with individual error handling
const handleBulkInvite = async (emails: string[], role: string) => {
  const results = [];
  for (const email of emails) {
    try {
      await inviteUser({ email, role, orgId, orgName });
      results.push({ email, status: 'success' });
    } catch (error) {
      results.push({ email, status: 'error', message: error.message });
    }
  }
  return results;
};
```

### 2. Role Management

#### Role Change Validation

```typescript
// Permission checking before role change
const canChangeUserRole = (
  currentUserRole: string,
  targetUserRole: string,
  newRole: string,
): { canChange: boolean; reason?: string } => {
  const currentLevel = ROLE_HIERARCHY[currentUserRole.toLowerCase()] || 0;
  const targetLevel = ROLE_HIERARCHY[targetUserRole.toLowerCase()] || 0;
  const newLevel = ROLE_HIERARCHY[newRole.toLowerCase()] || 0;

  // Cannot manage users at or above your level
  if (targetLevel >= currentLevel) {
    return {
      canChange: false,
      reason: `You don't have permission to manage users with ${targetUserRole} role`,
    };
  }

  // Cannot assign roles at or above your level
  if (newLevel >= currentLevel) {
    return {
      canChange: false,
      reason: `You don't have permission to assign ${newRole} role`,
    };
  }

  return { canChange: true };
};
```

#### Role Change Implementation

```typescript
const handleRoleChange = async (user: TeamMember, newRole: string) => {
  // Pre-validation
  const permissionCheck = canChangeUserRole(currentUserRole, user.role, newRole);
  if (!permissionCheck.canChange) {
    throw new Error(permissionCheck.reason);
  }

  // API call with optimistic updates
  await updateUserRole({
    email: user.email,
    role: newRole,
    orgId: currentOrgId,
    orgName: currentOrg?.orgName || '',
  });

  // Cache invalidation handled automatically by React Query
};
```

### 3. User Removal

#### Secure User Removal

```typescript
const handleUserRemoval = async (user: TeamMember) => {
  // Permission validation
  if (!canRemoveUser(currentUserRole, user.role)) {
    throw new Error('Insufficient permissions to remove this user');
  }

  // Confirmation dialog
  const confirmed = await showConfirmationDialog({
    title: 'Remove User',
    message: `Are you sure you want to remove ${user.email}? This action cannot be undone.`,
    confirmText: 'Remove',
    confirmVariant: 'destructive',
  });

  if (!confirmed) return;

  // API call
  await removeUser({
    email: user.email,
    orgId: currentOrgId,
    orgName: currentOrg?.orgName || '',
    reason: 'manual_removal',
  });

  // Immediate UI update
  refetchUsers();
};
```

---

## Role-Based Access Control (RBAC)

### Permission System Architecture

#### Core Permission Types

```typescript
enum Permission {
  // Device Management
  ViewDevices = 'view:devices',
  ManageDevices = 'manage:devices',

  // Team Management
  ViewTeam = 'view:team',
  ManageTeam = 'manage:team',

  // Profile Management
  ViewProfile = 'view:profile',
  EditProfile = 'edit:profile',

  // Administrative
  AdminAccess = 'admin:access',

  // Financial
  ViewEarnings = 'view:earnings',
  ManageEarnings = 'manage:earnings',
}
```

#### Role Hierarchy Implementation

```typescript
export const ROLE_HIERARCHY: Record<string, number> = {
  viewer: 1, // Read-only access
  member: 2, // Basic user with limited write access
  manager: 3, // Team management capabilities
  admin: 4, // Full administrative access
  superadmin: 5, // System-wide administration
  owner: 6, // Organization owner - highest authority
};

export const ROLE_MANAGEMENT_PERMISSIONS: Record<string, string[]> = {
  owner: ['viewer', 'member', 'manager', 'admin', 'superadmin'],
  superadmin: ['viewer', 'member', 'manager', 'admin'],
  admin: ['viewer', 'member', 'manager'],
  manager: ['viewer', 'member'],
  member: [], // Cannot manage roles
  viewer: [], // Cannot manage roles
};
```

#### Permission Mapping

```typescript
export const externalRoleToPermissions: ExternalRoleMapping = {
  viewer: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],
  member: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],
  manager: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],
  admin: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.AdminAccess,
    Permission.ViewEarnings,
    Permission.ManageEarnings,
    Permission.ViewJobs,
  ],
  // ... additional roles
};
```

### Client-Side Permission Guards

#### Component-Level Guards

```typescript
// Permission guard component
<OrgPermissionGuard
  permission={Permission.ManageTeam}
  fallback={<UnauthorizedMessage />}
>
  <TeamManagementInterface />
</OrgPermissionGuard>

// Conditional rendering based on permissions
const { hasPermission } = usePermissions();

return (
  <div>
    {hasPermission(Permission.ViewTeam) && <TeamMembersList />}
    {hasPermission(Permission.ManageTeam) && (
      <Button onClick={() => setInviteOpen(true)}>
        Invite Member
      </Button>
    )}
  </div>
);
```

#### Hook-Based Permission Checking

```typescript
// Custom permission hook
export const usePermissions = () => {
  const { data: session } = useSession();

  const hasPermission = useCallback(
    (permission: Permission) => {
      return (
        session?.permissions?.includes(permission) ||
        session?.permissions?.includes(Permission.AdminAccess)
      );
    },
    [session?.permissions],
  );

  const hasAnyPermission = useCallback(
    (permissions: Permission[]) => {
      return permissions.some((permission) => hasPermission(permission));
    },
    [hasPermission],
  );

  return {
    permissions: session?.permissions || [],
    hasPermission,
    hasAnyPermission,
    role: session?.role,
    isAdmin: hasPermission(Permission.AdminAccess),
  };
};
```

### Server-Side RBAC

#### API Route Protection

```typescript
// Server-side permission validation
import { requirePermission } from '@/lib/rbac';

export async function POST(request: NextRequest) {
  // Validate authentication and permissions
  await requirePermission(Permission.ManageTeam);

  // Validate input
  const body = await request.json();
  const validatedData = inviteUserSchema.parse(body);

  // Business logic validation
  const session = await getSession();
  if (session.email === validatedData.email) {
    return NextResponse.json({ error: 'Cannot invite yourself', success: false }, { status: 400 });
  }

  // Process request...
}
```

#### Permission Utilities

```typescript
// Server-side permission checking
export async function requirePermission(
  permission: Permission,
  redirectTo?: string,
): Promise<void> {
  const session = await getSession();

  if (!session) {
    if (redirectTo) redirect('/signin');
    throw new Error('Authentication required');
  }

  if (!hasPermission(session.permissions, permission)) {
    if (redirectTo) redirect('/unauthorized');
    throw new Error(`Permission denied: ${permission}`);
  }
}

// Route-based permission mapping
export const routePermissions: Record<string, Permission> = {
  '/team': Permission.ViewTeam,
  '/team/invite': Permission.ManageTeam,
  '/team/members': Permission.ViewTeam,
  '/devices': Permission.ViewDevices,
  '/devices/add': Permission.ManageDevices,
};
```

---

## API Integration & Hooks

### Modern React Query Implementation

#### Core API Hooks

```typescript
// Organization users with enhanced caching
export const useOrgUsers = (orgId: string, page: number, pageSize: number) => {
  return useQuery({
    queryKey: ['orgUsers', orgId, page, pageSize],
    queryFn: () => fetchOrgUsers(orgId, page, pageSize),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!orgId,
  });
};

// User management mutations with automatic cache invalidation
export const useInviteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: InviteUserRequest) =>
      apiClientWithInvalidation(
        '/api/orgs/users/add',
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        queryClient,
      ),
    onSuccess: (data, variables) => {
      // Optimistic updates
      queryClient.setQueryData(['orgUsers', variables.orgId], (old: OrgUsersResponse) => ({
        ...old,
        users: [
          ...old.users,
          {
            email: variables.email,
            role: variables.role,
            status: 'Invited',
          },
        ],
      }));
    },
  });
};
```

#### Cache Management Strategy

```typescript
// Intelligent cache invalidation
const apiClientWithInvalidation = async (
  url: string,
  options: RequestInit,
  queryClient: QueryClient,
) => {
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`API Error: ${response.status}`);
  }

  // Check for cache invalidation headers
  const invalidateHeader = response.headers.get('X-Invalidate-Queries');
  if (invalidateHeader) {
    const { queries, message } = JSON.parse(invalidateHeader);
    console.log(`🔄 Cache invalidation: ${message}`);

    queries.forEach((queryKey: string[]) => {
      queryClient.invalidateQueries({ queryKey });
    });
  }

  return response.json();
};

// Server-side cache invalidation headers
const nextResponse = NextResponse.json(result);
nextResponse.headers.set(
  'X-Invalidate-Queries',
  JSON.stringify({
    queries: [['orgUsers', orgId], ['userOrgRole', email, orgId], ['permissions']],
    message: 'User invited - refresh caches',
  }),
);
```

#### Error Handling Patterns

```typescript
// Structured error handling
interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// Global error handler
const handleApiError = (error: unknown): ApiError => {
  if (error instanceof Error) {
    return { message: error.message };
  }

  if (typeof error === 'object' && error !== null && 'message' in error) {
    return error as ApiError;
  }

  return { message: 'An unexpected error occurred' };
};

// Usage in components
const { mutate: inviteUser, error, isError } = useInviteUser();

// Error display
{isError && (
  <Alert variant="destructive">
    <AlertCircle className="h-4 w-4" />
    <AlertTitle>Error</AlertTitle>
    <AlertDescription>
      {handleApiError(error).message}
    </AlertDescription>
  </Alert>
)}
```

### API Endpoints Reference

#### User Management Endpoints

```typescript
// POST /api/orgs/users/add - Invite user
interface InviteUserRequest {
  email: string;
  role: string;
  orgId: string;
  orgName: string;
}

interface InviteUserResponse {
  success: boolean;
  message: string;
  user?: {
    email: string;
    role: string;
    status: string;
  };
  currentUserRole?: string;
}

// POST /api/orgs/users/update - Update user role
interface UpdateUserRoleRequest {
  email: string;
  role: string;
  orgId: string;
  orgName: string;
  accepted?: boolean;
  credits?: number;
}

// POST /api/orgs/users/remove - Remove user
interface RemoveUserRequest {
  email: string;
  orgId: string;
  orgName: string;
  reason?: string;
}
```

#### Validation Schemas

```typescript
// Zod schemas for API validation
const inviteUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.string().min(1, 'Role is required'),
  orgId: z.string().min(1, 'Organization ID is required'),
  orgName: z.string().min(1, 'Organization name is required'),
});

const updateUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.string().min(1, 'Role is required'),
  orgId: z.string().min(1, 'Organization ID is required'),
  orgName: z.string().min(1, 'Organization name is required'),
  accepted: z.boolean().optional(),
  credits: z.number().optional(),
});
```

---

## Component Architecture

### Core Component Patterns

#### TeamMembersTab Component

```typescript
interface TeamMembersTabProps {
  filteredUsers: TeamMember[];
  usersLoading: boolean;
  totalUserPages: number;
  pagination: PaginationState;
  roleFilter: string;
  setRoleFilter: (role: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  roles: Array<{ name: string }>;
  totalUsers: number;
  currentUserRole?: string | null;
  onChangeRole: (member: TeamMember) => void;
  onRemove: (member: TeamMember) => void;
}

export function TeamMembersTab({
  filteredUsers,
  usersLoading,
  totalUserPages,
  pagination,
  roleFilter,
  setRoleFilter,
  searchTerm,
  setSearchTerm,
  roles,
  totalUsers,
  currentUserRole,
  onChangeRole,
  onRemove
}: TeamMembersTabProps) {
  return (
    <Card className="bg-[#262131] border-[#3b3b3b] shadow-xl">
      <CardHeader className="pb-4">
        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <SearchInput
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="Search members by name or email..."
          />
          <RoleFilter
            value={roleFilter}
            onChange={setRoleFilter}
            roles={roles}
          />
        </div>
      </CardHeader>

      <CardContent>
        {/* Team Members Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-[#3b3b3b]">
                <th className="text-left py-3 px-4 text-[#94a3b8] font-medium">Member</th>
                <th className="text-left py-3 px-4 text-[#94a3b8] font-medium">Role</th>
                <th className="text-left py-3 px-4 text-[#94a3b8] font-medium">Status</th>
                <th className="text-left py-3 px-4 text-[#94a3b8] font-medium">Credits</th>
                <th className="text-left py-3 px-4 text-[#94a3b8] font-medium">Last Active</th>
                <th className="text-left py-3 px-4 text-[#94a3b8] font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {usersLoading ? (
                <TableSkeleton rows={pagination.pageSize} />
              ) : filteredUsers.length === 0 ? (
                <EmptyState message="No team members found" />
              ) : (
                filteredUsers.map((member) => (
                  <TeamMemberRow
                    key={member.email}
                    user={member}
                    currentUserRole={currentUserRole}
                    onChangeRole={() => onChangeRole(member)}
                    onRemove={() => onRemove(member)}
                  />
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        <PaginationControls
          page={pagination.page}
          totalPages={totalUserPages}
          onPrevious={pagination.goToPrevious}
          onNext={() => pagination.goToNext(totalUserPages)}
          pageSize={pagination.pageSize}
          onPageSizeChange={pagination.changePageSize}
          totalItems={totalUsers}
        />
      </CardContent>
    </Card>
  );
}
```

#### InviteModal Component

```typescript
interface InviteModalProps {
  open: boolean;
  onClose: () => void;
  onInvite: (email: string, role: string) => Promise<{
    message: string;
    currentUserRole?: string | null;
    error?: boolean;
  }>;
  orgName?: string;
  isLoading?: boolean;
}

export function InviteModal({
  open,
  onClose,
  onInvite,
  orgName,
  isLoading = false
}: InviteModalProps) {
  const [activeTab, setActiveTab] = useState('multiple');
  const [inviteResults, setInviteResults] = useState<InviteResult[]>([]);

  // Form configurations for different invitation modes
  const singleForm = useForm<SingleInviteForm>({
    resolver: zodResolver(singleInviteSchema),
    defaultValues: { email: '', role: '' }
  });

  const multipleForm = useForm<MultipleInviteForm>({
    resolver: zodResolver(multipleInviteSchema),
    defaultValues: { invites: [{ email: '', role: '' }] }
  });

  const bulkForm = useForm<BulkInviteForm>({
    resolver: zodResolver(bulkInviteSchema),
    defaultValues: { emails: '', role: '' }
  });

  // Handle different invitation types
  const handleSingleInvite = async (data: SingleInviteForm) => {
    const result = await onInvite(data.email, data.role);
    if (!result.error) {
      singleForm.reset();
      onClose();
    }
  };

  const handleMultipleInvite = async (data: MultipleInviteForm) => {
    const results: InviteResult[] = [];
    for (const invite of data.invites) {
      const result = await onInvite(invite.email, invite.role);
      results.push({
        email: invite.email,
        status: result.error ? 'error' : 'success',
        message: result.message,
        role: invite.role
      });
    }
    setInviteResults(results);
    if (results.every(r => r.status === 'success')) {
      multipleForm.reset();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl bg-[#1a1a2e] border-[#3b3b3b] text-white">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            Invite Members to {orgName}
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 bg-[#262131]">
            <TabsTrigger value="single">Single Invite</TabsTrigger>
            <TabsTrigger value="multiple">Multiple Invites</TabsTrigger>
            <TabsTrigger value="bulk">Bulk Invite</TabsTrigger>
          </TabsList>

          <TabsContent value="single">
            <SingleInviteForm
              form={singleForm}
              onSubmit={handleSingleInvite}
              isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="multiple">
            <MultipleInviteForm
              form={multipleForm}
              onSubmit={handleMultipleInvite}
              isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="bulk">
            <BulkInviteForm
              form={bulkForm}
              onSubmit={handleBulkInvite}
              isLoading={isLoading}
            />
          </TabsContent>
        </Tabs>

        {/* Results Display */}
        {inviteResults.length > 0 && (
          <InviteResultsDisplay results={inviteResults} />
        )}
      </DialogContent>
    </Dialog>
  );
}
```

#### Reusable UI Components

```typescript
// Search Input Component
export function SearchInput({
  value,
  onChange,
  placeholder,
  className = ""
}: SearchInputProps) {
  return (
    <div className={`relative flex-1 max-w-md ${className}`}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
      <Input
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-10 bg-[#1a1a2e] border-[#3b3b3b] text-white placeholder-gray-400 focus:border-purple-500"
      />
    </div>
  );
}

// Role Filter Component
export function RoleFilter({
  value,
  onChange,
  roles
}: RoleFilterProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="border-[#3b3b3b] text-white hover:bg-[#3b3b3b]">
          <Filter className="w-4 h-4 mr-2" />
          {value}
          <ChevronDown className="w-4 h-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-[#262131] border-[#3b3b3b]">
        <DropdownMenuItem onClick={() => onChange('All Roles')}>
          All Roles
        </DropdownMenuItem>
        {roles.map((role) => (
          <DropdownMenuItem key={role.name} onClick={() => onChange(role.name)}>
            {role.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Loading Skeleton Component
export function TableSkeleton({ rows = 5, cols = 6 }: TableSkeletonProps) {
  return (
    <>
      {Array.from({ length: rows }).map((_, i) => (
        <tr key={i}>
          {Array.from({ length: cols }).map((_, j) => (
            <td key={j} className="p-4">
              <Skeleton className="h-8 w-full bg-[#3b3b3b]" />
            </td>
          ))}
        </tr>
      ))}
    </>
  );
}

// Empty State Component
export function EmptyState({
  message,
  icon: Icon = Users,
  action
}: EmptyStateProps) {
  return (
    <tr>
      <td colSpan={6} className="text-center py-12">
        <div className="text-gray-300">
          <Icon className="w-16 h-16 mx-auto mb-6 opacity-60" />
          <p className="text-xl font-bold mb-3">{message}</p>
          {action && (
            <div className="mt-4">
              {action}
            </div>
          )}
        </div>
      </td>
    </tr>
  );
}
```

---

## State Management Patterns

### Local State Management

#### Pagination State Hook

```typescript
interface PaginationState {
  page: number;
  pageSize: number;
  goToPrevious: () => void;
  goToNext: (maxPages: number) => void;
  resetPage: () => void;
  changePageSize: (size: number) => void;
}

export const usePagination = (initialPage = 0, initialPageSize = 10): PaginationState => {
  const [page, setPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const goToPrevious = useCallback(() => {
    setPage((p) => Math.max(0, p - 1));
  }, []);

  const goToNext = useCallback((maxPages: number) => {
    setPage((p) => Math.min(maxPages - 1, p + 1));
  }, []);

  const resetPage = useCallback(() => {
    setPage(0);
  }, []);

  const changePageSize = useCallback((newSize: number) => {
    setPageSize(newSize);
    setPage(0); // Reset to first page when changing page size
  }, []);

  return {
    page,
    pageSize,
    goToPrevious,
    goToNext,
    resetPage,
    changePageSize,
  };
};
```

#### Debounced Search Hook

```typescript
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Usage in component
const [searchTerm, setSearchTerm] = useState('');
const debouncedSearchTerm = useDebounce(searchTerm, 300);

// Effect to handle search
useEffect(() => {
  if (debouncedSearchTerm !== searchTerm) {
    // Reset pagination when search changes
    usersPagination.resetPage();
  }
}, [debouncedSearchTerm]);
```

#### Complex State Management with useReducer

```typescript
// For complex state like modal management
interface ModalState {
  inviteOpen: boolean;
  removeUser: TeamMember | null;
  changeRoleUser: TeamMember | null;
  actionDialog: {
    open: boolean;
    message: string;
    type: 'success' | 'error';
  };
}

type ModalAction =
  | { type: 'OPEN_INVITE' }
  | { type: 'CLOSE_INVITE' }
  | { type: 'OPEN_REMOVE'; user: TeamMember }
  | { type: 'CLOSE_REMOVE' }
  | { type: 'OPEN_CHANGE_ROLE'; user: TeamMember }
  | { type: 'CLOSE_CHANGE_ROLE' }
  | { type: 'SHOW_ACTION_DIALOG'; message: string; dialogType: 'success' | 'error' }
  | { type: 'CLOSE_ACTION_DIALOG' };

const modalReducer = (state: ModalState, action: ModalAction): ModalState => {
  switch (action.type) {
    case 'OPEN_INVITE':
      return { ...state, inviteOpen: true };
    case 'CLOSE_INVITE':
      return { ...state, inviteOpen: false };
    case 'OPEN_REMOVE':
      return { ...state, removeUser: action.user };
    case 'CLOSE_REMOVE':
      return { ...state, removeUser: null };
    case 'OPEN_CHANGE_ROLE':
      return { ...state, changeRoleUser: action.user };
    case 'CLOSE_CHANGE_ROLE':
      return { ...state, changeRoleUser: null };
    case 'SHOW_ACTION_DIALOG':
      return {
        ...state,
        actionDialog: {
          open: true,
          message: action.message,
          type: action.dialogType,
        },
      };
    case 'CLOSE_ACTION_DIALOG':
      return {
        ...state,
        actionDialog: { ...state.actionDialog, open: false },
      };
    default:
      return state;
  }
};

// Usage in component
const [modalState, dispatch] = useReducer(modalReducer, {
  inviteOpen: false,
  removeUser: null,
  changeRoleUser: null,
  actionDialog: { open: false, message: '', type: 'success' },
});
```

### Global State with Context

#### Organization Context

```typescript
interface OrgContextType {
  currentOrg: Organization | null;
  currentOrgId: string | null;
  organizations: Organization[];
  switchOrganization: (orgId: string) => void;
  isLoading: boolean;
  error: string | null;
}

const OrgContext = createContext<OrgContextType | undefined>(undefined);

export function OrgProvider({ children }: { children: React.ReactNode }) {
  const [currentOrgId, setCurrentOrgId] = useState<string | null>(null);
  const { data: organizations, isLoading, error } = useOrganizations();

  const currentOrg = useMemo(() =>
    organizations?.find(org => org.id === currentOrgId) || null,
    [organizations, currentOrgId]
  );

  const switchOrganization = useCallback((orgId: string) => {
    setCurrentOrgId(orgId);
    // Invalidate org-specific queries
    queryClient.invalidateQueries({ queryKey: ['orgUsers', orgId] });
  }, []);

  const value = useMemo(() => ({
    currentOrg,
    currentOrgId,
    organizations: organizations || [],
    switchOrganization,
    isLoading,
    error: error?.message || null
  }), [currentOrg, currentOrgId, organizations, switchOrganization, isLoading, error]);

  return (
    <OrgContext.Provider value={value}>
      {children}
    </OrgContext.Provider>
  );
}

export const useOrgContext = () => {
  const context = useContext(OrgContext);
  if (context === undefined) {
    throw new Error('useOrgContext must be used within an OrgProvider');
  }
  return context;
};
```

---

## Workflows & Data Flow

### User Invitation Workflow

```mermaid
flowchart TD
    A[Click 'Invite Member'] --> B{Select Invitation Type}
    B -->|Single| C[Enter Email & Role]
    B -->|Multiple| D[Add Multiple Email/Role Pairs]
    B -->|Bulk| E[Paste Emails & Select Role]

    C --> F[Validate Email Format]
    D --> F
    E --> G[Parse & Validate Emails]
    G --> F

    F --> H{Valid Email?}
    H -->|No| I[Show Error Message]
    H -->|Yes| J[Check User Permissions]

    J --> K{Can Assign Role?}
    K -->|No| L[Show Permission Error]
    K -->|Yes| M[Send API Request]

    M --> N{API Success?}
    N -->|No| O[Show API Error]
    N -->|Yes| P[Show Success Message]
    P --> Q[Refresh User List]
    Q --> R[Close Modal]

    I --> S[Return to Form]
    L --> S
    O --> S
```

### Role Change Workflow

```mermaid
flowchart TD
    A[Click 'Change Role'] --> B[Open Role Selection Dialog]
    B --> C[Load Available Roles]
    C --> D{User Has Permission?}
    D -->|No| E[Show Limited/No Options]
    D -->|Yes| F[Show Available Roles]

    F --> G[User Selects New Role]
    G --> H[Validate Role Change]
    H --> I{Valid Change?}
    I -->|No| J[Show Validation Error]
    I -->|Yes| K[Show Confirmation Dialog]

    K --> L{User Confirms?}
    L -->|No| M[Cancel Operation]
    L -->|Yes| N[Send API Request]

    N --> O{API Success?}
    O -->|No| P[Show Error Message]
    O -->|Yes| Q[Show Success Message]
    Q --> R[Update User in List]
    R --> S[Close Dialog]

    J --> T[Return to Selection]
    P --> T
    M --> S
```

### Data Flow Architecture

```mermaid
graph TD
    A[User Action] --> B[Component Event Handler]
    B --> C[React Query Mutation]
    C --> D[API Route Handler]
    D --> E[Permission Validation]
    E --> F[Input Validation]
    F --> G[External API Call]
    G --> H[Database Update]
    H --> I[Response with Cache Headers]
    I --> J[React Query Cache Update]
    J --> K[Component Re-render]
    K --> L[UI Update]

    M[WebSocket Event] --> N[Real-time Update]
    N --> J
```

### Search and Filter Flow

```mermaid
flowchart TD
    A[User Input] --> B{Input Type}
    B -->|Search Text| C[Debounce Input 300ms]
    B -->|Role Filter| D[Apply Role Filter]
    B -->|Page Change| E[Update Pagination]

    C --> F[Filter Users by Name/Email]
    D --> G[Filter Users by Role]
    E --> H[Load New Page]

    F --> I[Combine with Role Filter]
    G --> J[Combine with Search Filter]

    I --> K[Apply Pagination]
    J --> K
    H --> K

    K --> L[Update User List Display]
    L --> M[Update UI State]
```

---

_Continuing with Performance Optimization, Security Implementation, Testing Strategy, and Best Practices..._
