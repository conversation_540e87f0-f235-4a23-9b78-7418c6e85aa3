# Role-Based Access Control (RBAC) with Multi-Organization Support

## Senior Software Engineer's Guide to Organization-Specific RBAC

This guide provides a comprehensive overview of implementing Role-Based Access Control (RBAC) with separate organization contexts, designed for enterprise-scale applications with 19k+ users across multiple organizations.

## 🏗️ Architecture Overview

### Core Components

1. **Permission System** (`lib/permissions.ts`)
2. **Role Management** (`lib/external-roles.ts`)
3. **React Hooks** (`hooks/use-permissions.tsx`)
4. **API Layer** (`app/api/auth/permissions/route.ts`)
5. **Caching Layer** (Redis with organization-aware keys)

### Key Design Principles

- **Organization Isolation**: Permissions are scoped to specific organizations
- **Performance First**: Multi-layer caching with Redis for 19k+ users
- **Type Safety**: Full TypeScript support throughout the system
- **Scalability**: Optimized for high-volume concurrent access
- **Security**: Principle of least privilege with organization boundaries

## 📊 Data Structure

### User-Organization-Role Relationship

```typescript
// Enhanced role structure for granular caching
interface UserOrgRole {
  emailId: string; // User identifier
  orgId: string; // Organization identifier
  role: string; // Role within organization
  orgName: string; // Human-readable org name
  updated: string; // ISO timestamp
}

// Cache key format: [emailId, orgId, role]
// Example: "<EMAIL>:org123:admin"
```

### Permission Hierarchy

```typescript
enum Permission {
  // Device Management
  ViewDevices = 'view:devices',
  ManageDevices = 'manage:devices',

  // Profile Management
  ViewProfile = 'view:profile',
  EditProfile = 'edit:profile',

  // Team Management
  ViewTeam = 'view:team',
  ManageTeam = 'manage:team',

  // Financial Access
  ViewEarnings = 'view:earnings',
  ManageEarnings = 'manage:earnings',

  // Job Management
  ViewJobs = 'view:jobs',
  ManageJobs = 'manage:jobs',

  // Administrative Access
  AdminAccess = 'admin:access',
}
```

### Role Hierarchy & Management

```typescript
// Role hierarchy levels - Higher number = Higher authority
export const ROLE_HIERARCHY: Record<string, number> = {
  viewer: 1, // Read-only access
  member: 2, // Basic user with limited write access
  manager: 3, // Team management capabilities
  admin: 4, // Full administrative access
  superadmin: 5, // System-wide administration
  owner: 6, // Organization owner - highest authority
};

// Role management permissions - who can manage whom
export const ROLE_MANAGEMENT_PERMISSIONS: Record<string, string[]> = {
  owner: ['viewer', 'member', 'manager', 'admin', 'superadmin'],
  superadmin: ['viewer', 'member', 'manager', 'admin'],
  admin: ['viewer', 'member', 'manager'],
  manager: ['viewer', 'member'],
  member: [], // Cannot manage roles
  viewer: [], // Cannot manage roles
};
```

### Role-to-Permission Mapping

```typescript
export const externalRoleToPermissions: ExternalRoleMapping = {
  // Viewer: Read-only access to most resources
  viewer: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],

  // Member: Viewer permissions + ability to edit own profile
  member: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.EditProfile, // Can edit their own profile
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],

  // Manager: Member permissions + team management
  manager: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam, // Can manage team members
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],

  // Admin: Full access except ownership actions
  admin: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewEarnings,
    Permission.ManageEarnings,
    Permission.ViewJobs,
    Permission.ManageJobs,
    Permission.AdminAccess, // Special admin-only features
  ],

  // SuperAdmin: Same as admin but with higher hierarchy level
  superadmin: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewEarnings,
    Permission.ManageEarnings,
    Permission.ViewJobs,
    Permission.ManageJobs,
    Permission.AdminAccess,
  ],

  // Owner: Complete control over the organization
  owner: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewEarnings,
    Permission.ManageEarnings,
    Permission.ViewJobs,
    Permission.ManageJobs,
    Permission.AdminAccess,
    // Note: Owners implicitly have all permissions
  ],
};
```

## 🔄 Permission Flow Architecture

### 1. Authentication & Session Management

```typescript
// User authenticates via Cognito/OAuth
// Session stored in Redis with organization context
const sessionData = {
  email: '<EMAIL>',
  userId: 'uuid',
  organizations: ['org1', 'org2'], // User's organizations
  currentOrgId: 'org1', // Active organization
};
```

### 2. Role Fetching & Caching

```typescript
// Multi-layer caching strategy
const cacheKeys = {
  // Email-based org roles (primary cache)
  orgRoles: `orgroles:${email}`,

  // Session-based simple roles (backward compatibility)
  sessionRoles: `session-roles:${sessionId}`,

  // Organization-specific permissions
  orgPermissions: `permissions:${email}:${orgId}`,
};
```

### 3. Permission Resolution

```mermaid
graph TD
    A[User Request] --> B{Has Session?}
    B -->|No| C[Redirect to Auth]
    B -->|Yes| D[Get Current Org]
    D --> E{Cache Hit?}
    E -->|Yes| F[Return Cached Permissions]
    E -->|No| G[Fetch from External API]
    G --> H[Process Organization Data]
    H --> I[Map Roles to Permissions]
    I --> J[Cache Results]
    J --> F
    F --> K[Apply Permission Guards]
```

## 🚀 Implementation Guide

### 1. Basic Permission Checking

```typescript
import { useOrgPermissions } from '@/hooks/use-permissions';
import { Permission } from '@/types/auth';

function DeviceManagementPanel() {
  const { hasPermission, currentOrgRole } = useOrgPermissions();

  if (!hasPermission(Permission.ManageDevices)) {
    return <AccessDenied />;
  }

  return (
    <div>
      <h1>Device Management</h1>
      <p>Your role: {currentOrgRole}</p>
      {/* Management interface */}
    </div>
  );
}
```

### 2. Organization-Specific Permissions

```typescript
import { useOrgPermissions } from '@/hooks/use-permissions';

function MultiOrgDashboard() {
  // Get permissions for specific organization
  const orgAPermissions = useOrgPermissions('org-a-id');
  const orgBPermissions = useOrgPermissions('org-b-id');

  return (
    <div>
      <h2>Organization A</h2>
      <p>Role: {orgAPermissions.currentOrgRole}</p>
      <p>Can manage devices: {orgAPermissions.hasPermission(Permission.ManageDevices) ? 'Yes' : 'No'}</p>

      <h2>Organization B</h2>
      <p>Role: {orgBPermissions.currentOrgRole}</p>
      <p>Can manage devices: {orgBPermissions.hasPermission(Permission.ManageDevices) ? 'Yes' : 'No'}</p>
    </div>
  );
}
```

### 3. Declarative Permission Guards

```typescript
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';

function ProtectedContent() {
  return (
    <div>
      {/* Admin-only content */}
      <OrgPermissionGuard
        role={['admin', 'owner']}
        fallback={<div>Admin access required</div>}
      >
        <AdminPanel />
      </OrgPermissionGuard>

      {/* Permission-based content */}
      <OrgPermissionGuard
        permission={Permission.ManageTeam}
        fallback={<div>Team management permission required</div>}
      >
        <TeamManagementPanel />
      </OrgPermissionGuard>

      {/* Multiple permissions (require all) */}
      <OrgPermissionGuard
        permission={[Permission.ViewEarnings, Permission.ManageEarnings]}
        requireAll={true}
        fallback={<div>Full earnings access required</div>}
      >
        <EarningsManagementPanel />
      </OrgPermissionGuard>
    </div>
  );
}
```

### 4. Organization Switching

```typescript
import { useOrgContext } from '@/components/org/org-context-provider';

function OrganizationSwitcher() {
  const {
    currentOrgId,
    organizations,
    switchOrganization,
    isLoading
  } = useOrgContext();

  return (
    <select
      value={currentOrgId || ''}
      onChange={(e) => switchOrganization(e.target.value)}
      disabled={isLoading}
    >
      {organizations.map(org => (
        <option key={org.orgId} value={org.orgId}>
          {org.orgName} ({org.role})
        </option>
      ))}
    </select>
  );
}
```

## 🔧 Advanced Features

### 1. Imperative Permission Checking

```typescript
import { useOrgPermissionCheck } from '@/components/auth/org-permission-guard';

function ConditionalFeatures() {
  const { checkAccess, checkPermission, checkRole } = useOrgPermissionCheck();

  const handleAction = () => {
    if (checkPermission(Permission.ManageDevices)) {
      // Perform device management action
    } else if (checkRole('admin')) {
      // Admin-specific action
    } else {
      // Show error or redirect
    }
  };

  const canAccessFeature = checkAccess(
    [Permission.ViewEarnings, Permission.ViewJobs], // permissions
    ['member', 'admin'],                            // roles
    false                                           // requireAll = false (any)
  );

  return (
    <div>
      <button onClick={handleAction}>
        Conditional Action
      </button>

      {canAccessFeature && (
        <AdvancedFeaturePanel />
      )}
    </div>
  );
}
```

### 2. Route-Level Protection

```typescript
// middleware.ts
import { getRoutePermission } from '@/lib/permissions';

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  const requiredPermission = getRoutePermission(pathname);

  if (requiredPermission) {
    const userPermissions = await getUserPermissions(request);

    if (!hasPermission(userPermissions, requiredPermission)) {
      return NextResponse.redirect(new URL('/unauthorized', request.url));
    }
  }

  return NextResponse.next();
}
```

### 3. API Endpoint Protection

```typescript
// app/api/devices/route.ts
import { hasPermission } from '@/lib/permissions';
import { Permission } from '@/types/auth';

export async function GET(request: NextRequest) {
  const userPermissions = await getUserPermissionsFromRequest(request);

  if (!hasPermission(userPermissions, Permission.ViewDevices)) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }

  // Return devices data
}

export async function POST(request: NextRequest) {
  const userPermissions = await getUserPermissionsFromRequest(request);

  if (!hasPermission(userPermissions, Permission.ManageDevices)) {
    return NextResponse.json({ error: 'Device management permission required' }, { status: 403 });
  }

  // Create/update device
}
```

## 🎯 Performance Optimization

### 1. Caching Strategy

```typescript
// Redis cache configuration for 19k+ users
const CACHE_CONFIG = {
  // Email-based org roles cache
  ORG_ROLES_TTL: 21600, // 6 hours
  ORG_ROLES_PREFIX: 'orgroles:', // orgroles:<EMAIL>

  // Session-based simple roles cache
  SESSION_ROLES_TTL: 3600, // 1 hour
  SESSION_PREFIX: 'session-roles:', // session-roles:sessionId

  // Permission cache
  PERMISSIONS_TTL: 1800, // 30 minutes
  PERMISSIONS_PREFIX: 'perms:', // perms:<EMAIL>:orgId
};

// Batch operations for bulk updates
const BATCH_SIZE = 50;
const API_RATE_LIMIT_DELAY = 100; // ms between API calls
```

### 2. Query Optimization

```typescript
// TanStack Query configuration for optimal performance
const queryConfig = {
  staleTime: 10 * 60 * 1000, // 10 minutes
  gcTime: 30 * 60 * 1000, // 30 minutes
  refetchOnWindowFocus: false, // Prevent unnecessary refetches
  refetchOnMount: true, // Ensure fresh data on mount
  retry: 2, // Limited retries
  retryDelay: 1000, // 1 second retry delay
};

// Dynamic query keys for granular cache control
const queryKeys = {
  permissions: (orgId?: string) =>
    orgId ? ['permissions', 'org', orgId] : ['permissions', 'global'],
  userOrgRole: (email: string, orgId: string) => ['userOrgRole', email, orgId],
  userAllOrgRoles: (email: string) => ['userOrgRoles', email],
};
```

### 3. Memory Management

```typescript
// Efficient data structures for large-scale operations
interface OptimizedUserCache {
  // Use Map for O(1) lookups
  orgRoles: Map<string, UserOrgRole>;
  permissions: Set<Permission>;
  lastUpdated: number;
}

// Lazy loading for organization data
const useOrganizationData = (orgId: string) => {
  return useQuery({
    queryKey: ['org', orgId],
    queryFn: () => fetchOrganizationData(orgId),
    enabled: !!orgId, // Only fetch when orgId is available
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};
```

## 🔒 Security Best Practices

### 1. Principle of Least Privilege

```typescript
// Default to most restrictive permissions
const DEFAULT_PERMISSIONS: Permission[] = [
  Permission.ViewProfile, // Basic profile access only
];

// Explicit permission grants
const grantPermissions = (basePermissions: Permission[], role: string): Permission[] => {
  const rolePermissions = externalRoleToPermissions[role] || [];
  return [...new Set([...basePermissions, ...rolePermissions])];
};
```

### 2. Organization Boundary Enforcement

```typescript
// Ensure users can only access their organizations
const validateOrganizationAccess = async (userId: string, orgId: string): Promise<boolean> => {
  const userOrgs = await getUserOrganizations(userId);
  return userOrgs.some((org) => org.orgId === orgId);
};

// API middleware for organization validation
export async function validateOrgAccess(request: NextRequest, orgId: string): Promise<boolean> {
  const session = await getSession(request);
  if (!session?.user?.email) return false;

  return validateOrganizationAccess(session.user.email, orgId);
}
```

### 3. Audit Logging

```typescript
// Comprehensive audit trail
interface AuditLog {
  userId: string;
  orgId: string;
  action: string;
  resource: string;
  permission: Permission;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  reason?: string;
}

const logPermissionCheck = async (audit: AuditLog): Promise<void> => {
  // Log to secure audit system
  await auditLogger.log({
    ...audit,
    level: audit.success ? 'info' : 'warn',
    category: 'permission_check',
  });
};
```

## 📈 Monitoring & Analytics

### 1. Performance Metrics

```typescript
// Key metrics to monitor
interface PermissionMetrics {
  cacheHitRate: number; // Redis cache performance
  avgResponseTime: number; // API response times
  permissionChecksPerSecond: number; // Load metrics
  failedPermissionChecks: number; // Security metrics
  organizationSwitches: number; // Usage patterns
}

// Real-time monitoring
const trackPermissionCheck = (
  permission: Permission,
  orgId: string,
  success: boolean,
  duration: number,
) => {
  metrics.increment('permission.checks.total');
  metrics.increment(`permission.checks.${success ? 'success' : 'failure'}`);
  metrics.histogram('permission.check.duration', duration);
  metrics.increment(`permission.org.${orgId}.checks`);
};
```

### 2. Cache Analytics

```typescript
// Cache performance monitoring
export async function getCacheAnalytics(): Promise<CacheAnalytics> {
  const redis = getRedisClient();

  // Get cache statistics
  const orgRoleKeys = await redis.keys('orgroles:*');
  const sessionKeys = await redis.keys('session-roles:*');
  const permissionKeys = await redis.keys('perms:*');

  return {
    totalKeys: orgRoleKeys.length + sessionKeys.length + permissionKeys.length,
    orgRoleCacheSize: orgRoleKeys.length,
    sessionCacheSize: sessionKeys.length,
    permissionCacheSize: permissionKeys.length,
    memoryUsage: await getRedisMemoryUsage(),
    hitRate: await calculateCacheHitRate(),
  };
}
```

## 🚀 Deployment Considerations

### 1. Environment Configuration

```typescript
// Environment-specific settings
const config = {
  development: {
    CACHE_TTL: 300, // 5 minutes for faster development
    LOG_LEVEL: 'debug', // Verbose logging
    RATE_LIMIT_DELAY: 0, // No rate limiting
  },
  staging: {
    CACHE_TTL: 1800, // 30 minutes
    LOG_LEVEL: 'info', // Standard logging
    RATE_LIMIT_DELAY: 50, // Light rate limiting
  },
  production: {
    CACHE_TTL: 21600, // 6 hours
    LOG_LEVEL: 'warn', // Error-focused logging
    RATE_LIMIT_DELAY: 100, // Full rate limiting
  },
};
```

### 2. Scaling Considerations

```typescript
// Horizontal scaling support
const REDIS_CLUSTER_CONFIG = {
  nodes: [
    { host: 'redis-1.internal', port: 6379 },
    { host: 'redis-2.internal', port: 6379 },
    { host: 'redis-3.internal', port: 6379 },
  ],
  options: {
    enableReadyCheck: true,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
  },
};

// Load balancing for API calls
const API_ENDPOINTS = [
  'https://api-1.qbraid.com',
  'https://api-2.qbraid.com',
  'https://api-3.qbraid.com',
];

const getApiEndpoint = (): string => {
  const index = Math.floor(Math.random() * API_ENDPOINTS.length);
  return API_ENDPOINTS[index];
};
```

## 🔄 Migration & Maintenance

### 1. Data Migration

```typescript
// Migrate from legacy role system to org-specific roles
export async function migrateLegacyRoles(): Promise<void> {
  const redis = getRedisClient();
  const legacyKeys = await redis.keys('roles:*');

  for (const legacyKey of legacyKeys) {
    const email = legacyKey.replace('roles:', '');
    const legacyRoles = JSON.parse((await redis.get(legacyKey)) || '[]');

    // Convert to org-specific format
    const orgRoles = await convertLegacyToOrgRoles(email, legacyRoles);

    // Store in new format
    await redis.setex(`orgroles:${email}`, ROLES_CACHE_TTL, JSON.stringify(orgRoles));

    // Remove legacy key
    await redis.del(legacyKey);
  }
}
```

### 2. Health Checks

```typescript
// System health monitoring
export async function performHealthCheck(): Promise<HealthStatus> {
  const checks = await Promise.allSettled([
    checkRedisHealth(),
    checkApiConnectivity(),
    checkCachePerformance(),
    checkPermissionConsistency(),
  ]);

  return {
    redis: checks[0].status === 'fulfilled',
    api: checks[1].status === 'fulfilled',
    cache: checks[2].status === 'fulfilled',
    permissions: checks[3].status === 'fulfilled',
    overall: checks.every((check) => check.status === 'fulfilled'),
    timestamp: new Date().toISOString(),
  };
}
```

## 📚 API Reference

### Core Functions

| Function                                  | Description                       | Parameters                   | Returns                |
| ----------------------------------------- | --------------------------------- | ---------------------------- | ---------------------- |
| `useOrgPermissions(orgId?)`               | Get permissions for organization  | `orgId?: string`             | `OrgPermissionsResult` |
| `hasPermission(permissions, required)`    | Check single permission           | `Permission[], Permission`   | `boolean`              |
| `hasAnyPermission(permissions, required)` | Check any of multiple permissions | `Permission[], Permission[]` | `boolean`              |
| `mapRolesToPermissions(roles)`            | Convert roles to permissions      | `string[]`                   | `Permission[]`         |
| `getCachedOrgRoles(email)`                | Get cached organization roles     | `string`                     | `UserOrgRoles \| null` |

### Hook Returns

```typescript
interface OrgPermissionsResult {
  // Data
  roles: string[];
  permissions: Permission[];
  orgRoles: OrgRoles;
  orgContext: OrgContext | null;

  // Organization context
  currentOrgId: string | null;
  currentOrgRole: string | null;
  isOrgSpecific: boolean;

  // Permission checking functions
  hasPermission: (permission: Permission) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  hasAllRoles: (roles: string[]) => boolean;

  // State
  isLoading: boolean;
  isRefreshing: boolean;
  hasError: boolean;
  error: Error | null;

  // Actions
  refreshPermissions: () => Promise<void>;
  refetch: () => Promise<void>;
}
```

---

## 🎯 Summary

This RBAC system provides:

- **🏢 Multi-Organization Support**: Complete isolation between organizations
- **⚡ High Performance**: Optimized for 19k+ users with multi-layer caching
- **🔒 Security First**: Principle of least privilege with audit logging
- **🎨 Developer Experience**: Type-safe hooks and declarative components
- **📈 Scalability**: Horizontal scaling support with Redis clustering
- **🔧 Maintainability**: Clean architecture with comprehensive monitoring

The system successfully handles complex enterprise requirements while maintaining simplicity for developers through well-designed abstractions and hooks.

## 🔧 Role Management Implementation

### Role Change Validation

```typescript
// Utility function to check if a user can change another user's role
export const canChangeUserRole = (
  currentUserRole: string,
  targetUserRole: string,
  newRole: string,
): { canChange: boolean; reason?: string } => {
  const currentLevel = ROLE_HIERARCHY[currentUserRole.toLowerCase()] || 0;
  const targetLevel = ROLE_HIERARCHY[targetUserRole.toLowerCase()] || 0;
  const newLevel = ROLE_HIERARCHY[newRole.toLowerCase()] || 0;

  // Cannot manage users at or above your level
  if (targetLevel >= currentLevel) {
    return {
      canChange: false,
      reason: `You don't have permission to manage users with ${targetUserRole} role`,
    };
  }

  // Cannot assign roles at or above your level
  if (newLevel >= currentLevel) {
    return {
      canChange: false,
      reason: `You don't have permission to assign ${newRole} role`,
    };
  }

  // Check if current user can manage the new role
  const managableRoles = ROLE_MANAGEMENT_PERMISSIONS[currentUserRole.toLowerCase()] || [];
  if (!managableRoles.includes(newRole.toLowerCase())) {
    return {
      canChange: false,
      reason: `You don't have permission to assign ${newRole} role`,
    };
  }

  return { canChange: true };
};
```

### Usage in Components

```typescript
// In team management component
const handleRoleChange = async (user: TeamMember, newRole: string) => {
  const validation = canChangeUserRole(currentUserRole, user.role, newRole);

  if (!validation.canChange) {
    setError(validation.reason);
    return;
  }

  try {
    await updateUserRole({
      email: user.email,
      role: newRole,
      orgId: currentOrgId,
      orgName: currentOrg?.orgName || '',
    });

    setSuccess(`Successfully updated ${user.email}'s role to ${newRole}`);
  } catch (error) {
    setError(`Failed to update role: ${error.message}`);
  }
};
```

## 🔧 Implementation Examples

For detailed implementation examples, see [RBAC_EXAMPLES.md](./RBAC_EXAMPLES.md).

---

_Last updated: 2025-01-14_
_Version: 2.1 - Added complete 6-tier role hierarchy and management validation_
